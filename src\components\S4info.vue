<template>
    <div>
        <div class="sense-main">
            <el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
            <br>
            <br>
            <span>&nbsp;&nbsp; 正盗版: &nbsp;</span>
            <el-select v-model="ispiracy" placeholder="请选择" clearable size="small">
                <el-option v-for="item in piracytype" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>&nbsp;&nbsp;&nbsp;
            <el-button type="primary" size="small" @click="getS4Info" round>查询数据</el-button>
            <br>
            <br>
            <el-table :data="paginatedData" border style="width: 100%">
                <el-table-column label="开发商id(16进制)" width="180">
                    <template slot-scope="scope">
                        {{ scope.row.s4info.developerid + '(' + scope.row.s4info.developeridhexstr + ')' }}
                    </template>
                </el-table-column>
                <el-table-column label="设备id(16进制)" width="240">
                    <template slot-scope="scope">
                        {{ scope.row.s4info.deviceid + '(' + scope.row.s4info.deviceidhexstr + ')' }}
                    </template>
                </el-table-column>
                <el-table-column prop="s4info.cosversion" label="版本信息" width="100">
                </el-table-column>
                <el-table-column prop="s4info.manufacture" label="生产日期" width="120">
                </el-table-column>
                <el-table-column prop="s4info.s4chipsn" label="锁号" width="180">
                </el-table-column>
                <el-table-column prop="s4info.detectid" label="detectid" width="120">
                </el-table-column>
                <el-table-column prop="s4info.freespace" label="剩余空间" width="120">
                </el-table-column>
                <el-table-column prop="s4info.drivermod" label="模式" width="100">
                </el-table-column>
                <el-table-column prop="s4info.usbpath" label="usb路径" width="700">
                </el-table-column>
            </el-table>
            <br>
            <el-pagination background @current-change="handlePageChange" :current-page="currentPage"
                :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="pagination" />
        </div>

    </div>

</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            currentPage: 1, // 当前页码
            pageSize: 10, // 每页条数
            total: 0,// 总条数
            s4datas: [],
            viewdataid: '',
            s4ispiracy: false,
            piracytype: [
                {
                    value: true,
                    label: '正版'
                },
                {
                    value: false,
                    label: '盗版'
                },
            ],
            ispiracy: '',
        }
    },
    // 获取viewdataid
    mounted() {
        this.s4ispiracy = this.$store.state.S4info.s4ispiracy;
        this.viewdataid = this.$store.state.Viewdataid.viewdataid;
        this.getS4Info();
    },
    computed: {
        // 计算当前页的数据
        paginatedData() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.s4datas.slice(start, end);
        }
    },
    methods: {
        // 页码切换时触发
        handlePageChange(page) {
            this.currentPage = page;
        },
        goback() {
            this.$router.go(-1);
        },
        // 获取s4info
        getS4Info() {
            let url = '/getpcs4infos';
            var params = {
                "viewdataid": this.viewdataid,
                "ispiracy": this.ispiracy
            }
            apiClient.post(url, params).catch(err => {
                this.$message.error("server:" + err.message);
                return
            }).then(res => {
                if (res.data.code == 0) {
                    this.total = res.data.data.length;
                    this.s4datas = res.data.data;
                    this.$message({
                        message: '获取数据成功',
                        type: 'success'
                    });
                } else {
                    this.$message({
                        message: res.data.msg,
                        type: 'warning'
                    });
                }
            })
        }
    },
}
</script>
