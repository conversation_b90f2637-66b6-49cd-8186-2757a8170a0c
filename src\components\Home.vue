<template>
	<el-row class="sense-container">
		<div class="sense-header">
			<div class="sense-header-box clearfix">
				<h1 class="fl" style="color: #fff;">盗版风控APM</h1>
				<div class="sense-nav fl">
					<el-menu default-active="1" class="el-menu-demo" mode="horizontal" text-color="#fff" background-color="#00479d" active-text-color="#fff"
					>
						<el-menu-item index="1">
							<router-link tag="span" to="/overview" class="tag-list">概览</router-link>
						</el-menu-item>
						<el-menu-item index="2">
							<router-link tag="span" to="/viewdata" class="tag-list">发现盗版</router-link>
						</el-menu-item>
						<el-menu-item index="3">
							<router-link tag="span" to="/allviewdata" class="tag-list">所有数据</router-link>	
						</el-menu-item>
						<el-menu-item index="4">
							<router-link tag="span" to="/definedhistory" class="tag-list">盗版打击历史</router-link>
						</el-menu-item>
					</el-menu>
				</div>
				<div class="sense-dropdown fr">
					<el-dropdown placement="bottom">
						<span class="el-dropdown-link clearfix">
							<span class="dev-name sense-ellipsis" :title="devName">{{ devName }}</span>
							<img :src="devLogo" class="dev-logo">
						</span>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item @click.native="setUp">
								<i class="ssicon ss-setup"></i>
								系统配置
							</el-dropdown-item>
							<el-dropdown-item @click.native="logout">
								<i class="ssicon ss-logout"></i>
								退出
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
				<!-- <div class="sense-notice fr">
					<el-dropdown trigger="click" @visible-change="noticeChange" :hide-on-click="isHideNotice">
						<span class="el-dropdown-link sense-notice-icon" :class="{ active: isNoticeShow }" title="消息通知">
							<i class="ssicon ss-bell"></i>
						</span>
						<el-dropdown-menu class="el-dropdown-menu-notice" slot="dropdown">
							<el-dropdown-item>
								<div class="sense-tabs">
									<el-tabs v-model="tabNameActive" @tab-click="handleNoticeTabClick">
										<el-tab-pane label="系统通知" name="1">
											<div class="notice-con"></div>
										</el-tab-pane>
										<el-tab-pane label="订阅消息" name="2">
											<div class="notice-con"></div>
										</el-tab-pane>
									</el-tabs>
								</div>
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div> -->
				<!-- <div class="sense-notice sense-upgrade fr">
					<el-dropdown trigger="click" @visible-change="upgradeChange" :hide-on-click="isHideNotice">
						<span class="el-dropdown-link sense-notice-icon" :class="{ active: isUpgradeShow }" title="反黑升级">
							<i class="ssicon ss-upgrade"></i>
						</span>
						<el-dropdown-menu class="el-dropdown-menu-notice el-dropdown-menu-upgrade" slot="dropdown">
							<el-dropdown-item>
								<div class="sense-tabs">
									<el-tabs v-model="tabUpgradeActive" @tab-click="handleUpgradeTabClick">
										<el-tab-pane label="安装包" name="1">
											<div class="upgrade-con">
												<div class="sense-no-data" v-if="packageList.length == 0">暂无数据</div>
												<div class="sense-tabs-list clearfix" v-for="item in packageList">
													<span class="fl sense-tabs-icon sense-tabs-icon-blue">
														<em class="ssicon ss-package"></em>
													</span>
													<div class="fl sense-tabs-text">
														<h3 class="sense-ellipsis" :title="item.name">{{ item.name }}
														</h3>
														<p>{{ item.createAt }}</p>
													</div>
													<div class="fr sense-tabs-operate">
														<a :href="'/download/getPkg.do?guid=' + item.guid"
															class="ssicon ss-download" title="下载"></a>
													</div>
												</div>
											</div>
										</el-tab-pane>
										<el-tab-pane label="反黑数据库" name="2">
											<div class="upgrade-con">
												<div class="sense-no-data" v-if="DBList.length == 0">暂无数据</div>
												<div class="sense-tabs-list clearfix" v-for="item in DBList">
													<span class="fl sense-tabs-icon sense-tabs-icon-red">
														<em class="ssicon ss-db"></em>
													</span>
													<div class="fl sense-tabs-text">
														<h3 class="sense-ellipsis" :title="item.name">{{ item.name }}
														</h3>
														<p>{{ item.createAt }}</p>
													</div>
													<div class="fr sense-tabs-operate">
														<a :href="'/download/getPkg.do?guid=' + item.guid"
															class="ssicon ss-download" title="下载"></a>
													</div>
												</div>
											</div>
										</el-tab-pane>
										<el-tab-pane label="授权" name="3">
											<div class="upgrade-con">
												<div class="sense-no-data" v-if="authorizeList.length == 0">暂无数据</div>
												<div class="sense-tabs-list clearfix" v-for="item in authorizeList">
													<span class="fl sense-tabs-icon sense-tabs-icon-yellow">
														<em class="ssicon ss-authorize"></em>
													</span>
													<div class="fl sense-tabs-text">
														<h3 class="sense-ellipsis">反黑授权</h3>
														<p>{{ item.createAt }}</p>
													</div>
													<div class="fr sense-tabs-operate">
														<a href="javascript:;" v-clipboard:copy="item.sn"
															v-clipboard:success="copySuccess">复制授权</a>
													</div>
												</div>
											</div>
										</el-tab-pane>
									</el-tabs>
								</div>
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div> -->
			</div>
		</div>
		<div class="sense-content">
			<transition name="fade" mode="out-in">
				<router-view></router-view>
			</transition>
		</div>
		<div class="sense-footer">
			北京深盾科技股份有限公司 &copy;
			<a target="_blank" href="https://beian.miit.gov.cn">京ICP备16009104号</a>
			<a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802025663">
				<img src="../img/keep-record.png">京公网安备 11010802025663号<br>
			</a>
		</div>
	</el-row>
</template>

<script>

export default {
	data() {
		return {
			isPreview: '',
			previewObj: {
				devName: 'Hello world',
				packageList: [],
				DBList: [],
				authorizeList: []
			},
			devLogo: require('../img/devLogo.png'),
			devName: '',
			isNoticeShow: false,  //消息通知
			isHideNotice: false,
			tabNameActive: '1',
			isUpgradeShow: false,  //反黑升级
			tabUpgradeActive: '1',
			packageList: [],  //反黑升级-安装包列表
			DBList: [],  //反黑升级-反黑数据库
			authorizeList: [],  //反黑升级-授权
		}
	},

	methods: {
		//获取开发者名称和logo
		getDevMessage() {
			if (this.isPreview == 1) {
				this.devName = this.previewObj.devName;
			} else {
				let userObj = JSON.parse(sessionStorage.getItem('userObj'));
				this.devName = userObj.name;
				if (userObj.logoUrl) {
					this.devLogo = 'data:image/png;base64,' + userObj.logoUrl;
				};
			};
		},
		setUp() {
			this.$router.push({
				path: '/setUp'
			})
		},
		logout() {
			this.$confirm('确认退出登录吗？', '退出登录', {
				type: 'warning'
			}).then(() => {
				localStorage.removeItem('jwt');
				this.$router.push('/login');
			}).catch(() => {
				
			});
		},
		//消息通知
		noticeChange(isShow) {
			this.isNoticeShow = isShow;
		},
		//反黑升级
		upgradeChange(isShow) {
			this.isUpgradeShow = isShow;
			if (isShow == true) {
				this.tabUpgradeActive = '1';

				//反黑升级-安装包列表
				this.getPackageList();
			};
		},
		//消息通知tab切换
		handleNoticeTabClick(tab, event) {

		},
		//反黑升级tab切换
		handleUpgradeTabClick(tab, event) {
			if (this.tabUpgradeActive == '1') {
				this.getPackageList();
			} else if (this.tabUpgradeActive == '2') {
				this.getDBList();
			} else {
				this.getAuthorizeList();
			};
		},
		//反黑升级-安装包列表
		getPackageList() {
			if (this.isPreview == 1) {
				this.packageList = this.previewObj.packageList;
			} else {
			};
		},
		//反黑升级-反黑DB列表
		getDBList() {
			if (this.isPreview == 1) {
				this.DBList = this.previewObj.DBList;
			} else {
			};
		},
		//反黑升级-授权列表
		getAuthorizeList() {
			if (this.isPreview == 1) {
				this.authorizeList = this.previewObj.authorizeList;
			} else {
			};
		},
		//复制授权成功
		copySuccess() {
			this.$message({
				message: '复制授权成功',
				type: 'success'
			});
		}
	},
	mounted() {
		this.isPreview = sessionStorage.getItem('isPreview');
	}
}

</script>

<style lang="scss">
$common-color: #409EFF;


</style>