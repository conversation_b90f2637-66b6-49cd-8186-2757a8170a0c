<template>
    <div id="container"></div>
  </template>
  <script>
  import AMapLoader from "@amap/amap-jsapi-loader";
  
  export default {
    name: "map-view",
    mounted() {
      this.initAMap();
    },
    unmounted() {
      this.map?.destroy();
    },
    methods: {
      initAMap() {
        window._AMapSecurityConfig = {
          securityJsCode: "d7b27200667cf8cc96fbe929e214d538",
        };
        AMapLoader.load({
          key: "c726cf8937bb6afc2a8fadcb89c4e8e4", // 申请好的Web端开发者Key，首次调用 load 时必填
          version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
          plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        })
          .then((AMap) => {
            this.map = new AMap.Map("container", {
              // 设置地图容器id
              viewMode: "3D", // 是否为3D地图模式
              zoom: 17.7, // 初始化地图级别
              center: [116.29472, 39.94696], // 初始化地图中心点位置
            });
          })
          .catch((e) => {
            console.log(e);
          });
      },
    },
  };
  </script>
  <style scoped>
  #container {
    width: 100%;
    height: 800px;
  }
  </style>
  