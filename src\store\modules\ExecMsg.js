const state = {
    hostname: "",
    companyname: "",
    appid: "",
    guid: "",
}

const mutations = {
    SET_EXEC_HOSTNAME: (state, hostname) => {
        state.hostname = hostname 
    },
    SET_EXEC_COMPANYNAME: (state, companyname) => {
        state.companyname = companyname
    },
    SET_EXEC_APPID: (state, appid) => {
        state.appid = appid
    },
    SET_EXEC_GUID: (state, guid) => {
        state.guid = guid
    },
}

const getters = {}

const actions = {}

export default {
    state,
    getters,
    actions,
    mutations
}