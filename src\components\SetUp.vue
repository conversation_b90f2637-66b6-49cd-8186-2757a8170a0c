<template>
	<div>
		<div class="sense-main" style="padding: 0;">
			<el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
			<br><br>
			<el-tabs type="border-card" v-model="tabsActive" @tab-click="handleClick">
				<!-- 公司配置 -->
				<el-tab-pane label="公司配置" name="1">
					<el-tabs v-model="companyactive" @tab-click="companyClick" :tab-position="'left'" type="border-card"
						style="margin: 1% 15% ; height: 45rem;">
						<el-tab-pane label="公司信息管理" name="1">
							<el-input placeholder="请输入公司名称,为空查询所有" style="width: 40%;"
								v-model="companyinput"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchcompany">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addcompanyvisiable = true">添加新公司记录</el-button>
							<el-dialog title="添加公司记录" :visible.sync="addcompanyvisiable" width="30%"
								style="margin-top: 5%;" @close="closeaddcompany">
								<el-input placeholder="请输入新公司名称" style="width: 50%;margin-left: 20%;"
									v-model="addcompanyname"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addcompanyvisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										type="primary" @click="addCompanyName">添 加</el-button>
								</span>
							</el-dialog>

							<br><br>
							<el-table :data="companycurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="id" label="唯一ID" width="200">
								</el-table-column>
								<el-table-column prop="companyname" label="公司名称" width="200">
								</el-table-column>
								<el-table-column prop="username" label="记录用户" width="130">
								</el-table-column>
								<el-table-column prop="lasttime" label="记录时间" width="180">
								</el-table-column>
								<el-table-column label="操作" width="260">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changecomnamevisiable(scope.row.id)">修改名称</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="opendeletecompanymessage(scope.row.id)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改公司名称 -->
							<el-dialog title="修改公司名称" :visible.sync="changecompanynamevisiable" width="30%"
								style="margin-top: 5%;" @close="closechangecompany">
								<el-input placeholder="请输入新公司名称" style="width: 50%;margin-left: 20%;"
									v-model="changecomname"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changecompanynamevisiable = false">取 消</el-button>
									<el-button style="margin-left: 5%;" type="primary" @click="changeCompanyName">修
										改</el-button>
								</span>
							</el-dialog>
							<div class="sense-table-pager" v-if="companytotal > 0">
								<el-pagination background @size-change="companyhandleSizeChange"
									@current-change="companyhandleCurrentChange" :current-page="comapnycurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="companypageSize"
									layout="total, sizes, prev, pager, next, jumper" :total="companytotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- ip映射 -->
						<el-tab-pane label="IP映射管理" name="2">
							<el-input placeholder="请输入查询IP,为空查询所有" v-model="ipinput" style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchip">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
								@click="addIPcompanyvisiable" type="primary">添加新IP纪录</el-button>
							<el-dialog title="添加IP记录" width="30%" :visible.sync="addipvisiable" style="margin-top: 5%;"
								@close="closeaddip">
								<el-input v-model="addipinput" placeholder="请输入IP地址"
									style="width: 50%;margin-left: 20%;"></el-input>
								<el-select v-model="selectcompanyid" placeholder="请选择公司名称"
									style="width: 50%;margin-left: 20%;margin-top: 5%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id">
									</el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addipvisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addIPcompany" type="primary">添 加</el-button>
								</span>
							</el-dialog>

							<!-- 修改ip对应公司 -->
							<el-dialog title="修改IP对应公司" width="30%" style="margin-top: 5%;"
								:visible.sync="changeipvisiable" @close="closeChangeIP">
								<el-descriptions :column="1" border>
									<el-descriptions-item label="当前IP">{{ curip
									}}</el-descriptions-item>
									<el-descriptions-item label="当前公司">{{ curcompanyname
									}}</el-descriptions-item>
								</el-descriptions><br>
								<el-select v-model="selectcompanyid" placeholder="请选择新的公司"
									style="width: 50%;margin-left: 20%;margin-top: 3%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id">
									</el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changeipvisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" @click="changeIPcompany"
										type="primary">修
										改</el-button>
								</span>
							</el-dialog>

							<br><br>
							<el-table :data="ipcurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="ip" label="IP" width="200">
								</el-table-column>
								<el-table-column prop="companyname" label="公司名称" width="200">
								</el-table-column>
								<el-table-column prop="username" label="记录用户" width="130">
								</el-table-column>
								<el-table-column prop="lasttime" label="记录时间" width="180">
								</el-table-column>
								<el-table-column label="操作" width="260">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeIPshow(scope.row)">修改公司</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteIPshow(scope.row.ip)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div class="sense-table-pager" v-if="iptotal > 0">
								<el-pagination background @size-change="iphandleSizeChange"
									@current-change="iphandleCurrentChange" :current-page="ipcurpage"
									:page-sizes="[10, 20, 30]" :page-size="ippagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="iptotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- wifi路由 -->
						<el-tab-pane label="Wifi路由管理" name="3">
							<el-input placeholder="请输入查询WIFI,为空查询所有" v-model="wifiinput" style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchwifi">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
								@click="addWIFIcompanyvisiable" type="primary">添加新WIFI纪录</el-button>
							<el-dialog title="添加WIFI记录" width="30%" :visible.sync="addwifivisiable"
								style="margin-top: 5%;" @close="closeaddwifi">
								<el-input v-model="addwifiinput" placeholder="请输入MAC地址"
									style="width: 50%;margin-left: 20%;"></el-input>
								<el-select v-model="selectcompanyid" placeholder="请选择公司名称"
									style="width: 50%;margin-left: 20%;margin-top: 5%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id">
									</el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addwifivisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addWIFIcompany" type="primary">添 加</el-button>
								</span>
							</el-dialog>

							<!-- 修改wifi对应公司 -->
							<el-dialog title="修改WIFI对应公司" width="30%" style="margin-top: 5%;"
								:visible.sync="changewifivisiable" @close="closeChangeWIFI">
								<el-descriptions :column="1" border>
									<el-descriptions-item label="当前WIFI">{{ curwifi
									}}</el-descriptions-item>
									<el-descriptions-item label="当前公司">{{ curcompanyname
									}}</el-descriptions-item>
								</el-descriptions><br>
								<el-select v-model="selectcompanyid" placeholder="请选择新的公司"
									style="width: 50%;margin-left: 20%;margin-top: 3%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id">
									</el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changewifivisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" @click="changeWIFIcompany"
										type="primary">修
										改</el-button>
								</span>
							</el-dialog>

							<br><br>
							<el-table :data="wificurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="wifirouter" label="WIFI" width="200">
								</el-table-column>
								<el-table-column prop="companyname" label="公司名称" width="200">
								</el-table-column>
								<el-table-column prop="username" label="记录用户" width="130">
								</el-table-column>
								<el-table-column prop="lasttime" label="记录时间" width="180">
								</el-table-column>
								<el-table-column label="操作" width="260">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeWIFIshow(scope.row)">修改公司</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteWIFIshow(scope.row.wifirouter)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div class="sense-table-pager" v-if="wifitotal > 0">
								<el-pagination background @size-change="wifihandleSizeChange"
									@current-change="wifihandleCurrentChange" :current-page="wificurpage"
									:page-sizes="[10, 20, 30]" :page-size="wifipagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="wifitotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- 域名映射 -->
						<el-tab-pane label="域名映射管理" name="4">
							<el-input placeholder="请输入查询域名,为空查询所有" v-model="domaininput" style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchdomain">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addDomaincompanyvisiable">添加新域名记录</el-button>
							<el-dialog title="添加域名记录" width="30%" :visible.sync="addDomainVisiable"
								style="margin-top: 5%;" @close="closeAddDomain">
								<el-input v-model="addDomainInput" placeholder="请输入域名"
									style="width: 50%;margin-left: 20%;"></el-input>
								<el-select v-model="selectcompanyid" placeholder="请选择公司名称"
									style="width: 50%;margin-left: 20%;margin-top: 5%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id"></el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addDomainVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addDomain" type="primary">添
										加</el-button>
								</span>
							</el-dialog>

							<!-- 修改域名对应公司 -->
							<el-dialog title="修改域名对应公司" width="30%" style="margin-top: 5%;"
								:visible.sync="changeDomainVisiable" @close="closeChangeDomain">
								<el-descriptions :column="1" border>
									<el-descriptions-item label="当前域名">{{ curdomain }}</el-descriptions-item>
									<el-descriptions-item label="当前公司">{{ curcompanyname }}</el-descriptions-item>
								</el-descriptions><br>
								<el-select v-model="selectcompanyid" placeholder="请选择新的公司"
									style="width: 50%;margin-left: 20%;margin-top: 3%;">
									<el-option v-for="item in companylist" :key="item.id" :label="item.companyname"
										:value="item.id"></el-option>
								</el-select>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changeDomainVisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" @click="changeDomain"
										type="primary">修 改</el-button>
								</span>
							</el-dialog>

							<br><br>
							<el-table :data="domaincurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="domain" label="域名" width="200"></el-table-column>
								<el-table-column prop="companyname" label="公司名称" width="200"></el-table-column>
								<el-table-column prop="username" label="记录用户" width="130"></el-table-column>
								<el-table-column prop="lasttime" label="记录时间" width="180"></el-table-column>
								<el-table-column label="操作" width="260">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeDomainShow(scope.row)">修改公司</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteDomainShow(scope.row.domain)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div class="sense-table-pager" v-if="domaintotal > 0">
								<el-pagination background @size-change="domainhandleSizeChange"
									@current-change="domainhandleCurrentChange" :current-page="domaincurpage"
									:page-sizes="[10, 20, 30]" :page-size="domainpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="domaintotal">
								</el-pagination>
							</div>
						</el-tab-pane>
					</el-tabs>
				</el-tab-pane>
				<!-- 黑名单配置 -->
				<el-tab-pane label="黑名单配置" name="2">
					<el-tabs v-model="blacklisttabActive" @tab-click="blacklisthandleTabClick" :tab-position="'left'"
						type="border-card" style="margin: 1% 15% ; height: 45rem;">
						<!-- 机械黑名单 -->
						<el-tab-pane label="机器黑名单管理" name="1">
							<el-input placeholder="请输入查询机器号,为空查询所有" v-model="guidinput" style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchguid">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addguidVisiable = true">添加新机器黑名单</el-button>
							<!-- 添加guid记录 -->
							<el-dialog title="添加机器黑名单" width="30%" :visible.sync="addguidVisiable"
								style="margin-top: 5%;" @close="closeAddGuid">
								<el-input v-model="addguidInput" placeholder="请输入GUID"
									style="width: 50%;margin-left: 20%;"></el-input>
								<br><br>
								<el-input v-model="addguidremarkInput" placeholder="请输入标记信息"
									style="width: 50%;margin-left: 20%;"></el-input>

								<span slot="footer" class="dialog-footer">
									<el-button @click="addguidVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addGuid" type="primary">添
										加</el-button>
								</span>
							</el-dialog>
							<br><br>
							<el-table :data="guidcurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="guid" label="GUID" width="300">
								</el-table-column>
								<el-table-column prop="remark" label="标记信息" width="180">
								</el-table-column>
								<el-table-column prop="username" label="标记用户" width="100">
								</el-table-column>
								<el-table-column prop="lasttime" label="标记时间" width="160">
								</el-table-column>
								<el-table-column label="操作" width="220">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeremarkvisiable(scope.row.guid)">修改标记</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteGuidShow(scope.row.guid)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改标记信息 -->
							<el-dialog title="修改标记信息" :visible.sync="changeguidremarkvisiable" width="30%"
								style="margin-top: 5%;" @close="closechangeremark">
								<el-input placeholder="请输入标记信息" style="width: 50%;margin-left: 20%;"
									v-model="changeguidremarkinput"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changeguidremarkvisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" type="primary"
										@click="changeGuid">修
										改</el-button>
								</span>
							</el-dialog>

							<div class="sense-table-pager" v-if="guidtotal > 0">
								<el-pagination background @size-change="guidhandleSizeChange"
									@current-change="guidhandleCurrentChange" :current-page="guidcurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="guidpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="guidtotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- 芯片黑名单 -->
						<el-tab-pane label="芯片号黑名单管理" name="2">
							<el-input placeholder="请输入查询芯片号,为空查询所有" v-model="chipsninput"
								style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchchipsn">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addchipsnVisiable = true">添加芯片号黑名单</el-button>
							<!-- 添加guid记录 -->
							<el-dialog title="添加芯片号黑名单" width="30%" :visible.sync="addchipsnVisiable"
								style="margin-top: 5%;" @close="closeAddChipsn">
								<el-input v-model="addchipsnInput" placeholder="请输入Chipsn-16进制字符串"
									style="width: 50%;margin-left: 20%;"></el-input>
								<br><br>
								<el-input v-model="addchipsnremarkInput" placeholder="请输入标记信息"
									style="width: 50%;margin-left: 20%;"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addchipsnVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addChipsn" type="primary">添
										加</el-button>
								</span>
							</el-dialog>
							<br><br>
							<el-table :data="chipsncurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="chipsnhex" label="芯片号" width="310">
								</el-table-column>
								<el-table-column prop="remark" label="标记信息" width="180">
								</el-table-column>
								<el-table-column prop="username" label="标记用户" width="100">
								</el-table-column>
								<el-table-column prop="lasttime" label="标记时间" width="160">
								</el-table-column>
								<el-table-column label="操作" width="220">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeChipsnshow(scope.row.chipsnhex)">修改标记</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteChipsnShow(scope.row.chipsnhex)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改标记信息 -->
							<el-dialog title="修改标记信息" :visible.sync="changechipsnremarkvisiable" width="30%"
								style="margin-top: 5%;" @close="closeChangeChipsn">
								<el-input placeholder="请输入标记信息" style="width: 50%;margin-left: 20%;"
									v-model="changechipsnremarkinput"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changechipsnremarkvisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" type="primary"
										@click="changeChipsn">修
										改</el-button>
								</span>
							</el-dialog>

							<div class="sense-table-pager" v-if="guidtotal > 0">
								<el-pagination background @size-change="chipsnhandleSizeChange"
									@current-change="chipsnhandleCurrentChange" :current-page="chipsncurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="chipsnpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="chipsntotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- 外壳号黑名单管理 -->
						<el-tab-pane label="外壳号黑名单管理" name="3">
							<el-input placeholder="请输入查询外壳号,为空查询所有" v-model="casesninput"
								style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchcasesn">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addcasesnVisiable = true">添加外壳号黑名单</el-button>
							<!-- 添加guid记录 -->
							<el-dialog title="添加外壳号黑名单" width="30%" :visible.sync="addcasesnVisiable"
								style="margin-top: 5%;" @close="closeAddCasesn">
								<el-input v-model="addcasesnInput" placeholder="请输入外壳号"
									style="width: 50%;margin-left: 20%;"></el-input>
								<br><br>
								<el-input v-model="addcasesnremarkInput" placeholder="请输入标记信息"
									style="width: 50%;margin-left: 20%;"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addcasesnVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addCasesn" type="primary">添
										加</el-button>
								</span>
							</el-dialog>
							<br><br>
							<el-table :data="casesncurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="casesn" label="外壳号" width="300">
								</el-table-column>
								<el-table-column prop="remark" label="标记信息" width="180">
								</el-table-column>
								<el-table-column prop="username" label="标记用户" width="100">
								</el-table-column>
								<el-table-column prop="lasttime" label="标记时间" width="160">
								</el-table-column>
								<el-table-column label="操作" width="220">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeCasesnshow(scope.row.casesn)">修改标记</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteCasesnShow(scope.row.casesn)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改标记信息 -->
							<el-dialog title="修改标记信息" :visible.sync="changecasesnremarkvisiable" width="30%"
								style="margin-top: 5%;" @close="closeChangeCasesn">
								<el-input placeholder="请输入标记信息" style="width: 50%;margin-left: 20%;"
									v-model="changecasesnremarkinput"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changecasesnremarkvisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" type="primary"
										@click="changeCasesn">修
										改</el-button>
								</span>
							</el-dialog>

							<div class="sense-table-pager" v-if="casesntotal > 0">
								<el-pagination background @size-change="casesnhandleSizeChange"
									@current-change="casesnhandleCurrentChange" :current-page="casesncurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="casesnpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="casesntotal">
								</el-pagination>
							</div>
						</el-tab-pane>
						<!-- AHX黑名单管理 -->
						<el-tab-pane label="AHX黑名单管理" name="4">
							<el-input placeholder="请输入查询Ahxid,为空查询所有" v-model="ahxidinput"
								style="width: 40%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchahxid">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addahxidVisiable = true">添加外壳号黑名单</el-button>
							<!-- 添加guid记录 -->
							<el-dialog title="添加外壳号黑名单" width="30%" :visible.sync="addahxidVisiable"
								style="margin-top: 5%;" @close="closeAddCasesn">
								<el-input v-model="addahxidInput" placeholder="请输入外壳号"
									style="width: 50%;margin-left: 20%;"></el-input>
								<br><br>
								<el-input v-model="addahxidremarkInput" placeholder="请输入标记信息"
									style="width: 50%;margin-left: 20%;"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addahxidVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										@click="addAhxid" type="primary">添
										加</el-button>
								</span>
							</el-dialog>
							<br><br>
							<el-table :data="ahxidcurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="ahxid" label="Ahxid" width="300">
								</el-table-column>
								<el-table-column prop="remark" label="标记信息" width="180">
								</el-table-column>
								<el-table-column prop="username" label="标记用户" width="100">
								</el-table-column>
								<el-table-column prop="lasttime" label="标记时间" width="160">
								</el-table-column>
								<el-table-column label="操作" width="220">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeAhxidshow(scope.row.ahxid)">修改标记</el-button>
										<el-button type="danger" style="margin-left: 10%;" size="small"
											@click="deleteAhxidShow(scope.row.ahxid)">删除记录</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改标记信息 -->
							<el-dialog title="修改标记信息" :visible.sync="changeahxidremarkvisiable" width="30%"
								style="margin-top: 5%;" @close="closeChangeAhxid">
								<el-input placeholder="请输入标记信息" style="width: 50%;margin-left: 20%;"
									v-model="changeahxidremarkinput"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changeahxidremarkvisiable = false">取 消</el-button>
									<el-button icon="el-icon-edit" style="margin-left: 5%;" type="primary"
										@click="changeAhxid">修
										改</el-button>
								</span>
							</el-dialog>

							<div class="sense-table-pager" v-if="ahxidtotal > 0">
								<el-pagination background @size-change="ahxidhandleSizeChange"
									@current-change="ahxidhandleSizeChange" :current-page="ahxidcurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="ahxidpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="ahxidtotal">
								</el-pagination>
							</div>
						</el-tab-pane>
					</el-tabs>
				</el-tab-pane>
				<!-- app管理 -->
				<el-tab-pane label="app管理" name="3">
					<div style="margin: 1% 15% ; height: 45rem;">
						<el-card shadow="always" style="height: 100%;">
							<el-input placeholder="请输入App名称,为空查询所有" style="width: 40%;margin-left: 20%;"
								v-model="appinput"></el-input>
							<el-button type="primary" icon="el-icon-search" style="margin-left: 5%;"
								@click="searchapp">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addappVisiable = true">添加新App记录</el-button>

							<el-dialog title="添加App记录" :visible.sync="addappVisiable" width="30%"
								style="margin-top: 5%;" @close="closeAddApp">
								<span>Appid : </span>
								<el-input placeholder="请输入新Appid" style="width: 50%;margin-left: 20%;"
									v-model="addappidInput"></el-input>
								<br>
								<br>
								<span>App名称 : </span>
								<el-input placeholder="请输入新App名称" style="width: 50%;margin-left: 17%;"
									v-model="addappnameInput"></el-input>
								<!-- 文件校验模式 -->
								<br>
								<br>
								<span>文件校验模式 : </span>
								<el-select v-model="addhashmodeInput" placeholder="请选择" style="width: 50%;margin-left: 11%;">
									<el-option label="宽松模式" value=0></el-option>
									<el-option label="严格模式" value=1></el-option>
								</el-select>
								<br>
								<br>
								<el-descriptions title="" direction="vertical" :column="1" border>
									<el-descriptions-item label="宽松模式">宽松模式下，仅校验上传的Hash。</el-descriptions-item>
									<el-descriptions-item label="严格模式">严格模式下，会校验当前APP开启的所有文件Hash，缺少则属于异常。</el-descriptions-item>
								</el-descriptions>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addappVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;"
										type="primary" @click="addApp">添 加</el-button>
								</span>
							</el-dialog>
							<br>
							<br>
							<el-table :data="appcurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="appid" label="Appid" width="100">
								</el-table-column>
								<el-table-column prop="appname" label="App名称" width="150">
								</el-table-column>
								<el-table-column prop="username" label="记录用户" width="130">
								</el-table-column>
								<el-table-column prop="hashmode" label="文件Hash模式" width="130">
								</el-table-column>
								<el-table-column prop="time" label="记录时间" width="200">
								</el-table-column>
								<el-table-column label="操作" width="350">
									<template v-slot="scope">
										<el-button type="warning" size="small"
											@click="changeAppshow(scope.row.appid, scope.row.appname)">修改名称</el-button>
										<el-button type="danger" style="margin-left: 5%;" size="small"
											@click="deleteAppShow(scope.row.appid)">删除记录</el-button>
										<el-button type="warning" style="margin-left: 5%;" size="small"
											@click="changeHashShow(scope.row.appid)">修改Hash模式</el-button>
									</template>
								</el-table-column>
							</el-table>
							<!-- 修改app名称 -->
							<el-dialog title="修改App名称" :visible.sync="changeappVisiable" width="30%"
								style="margin-top: 5%;" @close="closeChangeApp">
								<el-descriptions title="" direction="vertical" :column="1" border>
									<el-descriptions-item label="当前Appid">{{ curappid }}</el-descriptions-item>
									<el-descriptions-item label="当前App名称">{{ curappname }}</el-descriptions-item>
								</el-descriptions>
								<br>
								<span>新名称 : </span>
								<el-input placeholder="请输入新App名称" style="width: 50%;margin-left: 2%;"
									v-model="changeappremarkInput"></el-input>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changeappVisiable = false">取 消</el-button>
									<el-button style="margin-left: 5%;" type="primary" @click="changeApp">修
										改</el-button>
								</span>
							</el-dialog>
							<!-- 修改Hash模式 -->
							<el-dialog title="修改Hash模式" :visible.sync="changehashVisiable" width="30%"
								style="margin-top: 5%;" @close="closeChangeHash">
								<br>
								<span>Hash模式选择 : </span>
								<!-- 下拉框 -->
								<el-select v-model="changehashinput" placeholder="请选择" style="width: 50%;margin-left: 2%;">
									<el-option label="宽松模式" value=0></el-option>
									<el-option label="严格模式" value=1></el-option>
								</el-select>
								<br>
								<br>
								<!-- 模式描述 -->
								<el-descriptions title="" direction="vertical" :column="1" border>
									<el-descriptions-item label="宽松模式">宽松模式下，仅校验上传的Hash。</el-descriptions-item>
									<el-descriptions-item label="严格模式">严格模式下，会校验当前APP开启的所有文件Hash，缺少则属于异常。</el-descriptions-item>
								</el-descriptions>
								<br>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changehashVisiable = false">取 消</el-button>
									<el-button style="margin-left: 5%;" type="primary" @click="changeHashMode">修
										改</el-button>
								</span>
							</el-dialog>

							<div class="sense-table-pager" v-if="apptotal > 0">
								<el-pagination background @size-change="apphandleSizeChange"
									@current-change="apphandleCurrentChange" :current-page="appcurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="apppagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="apptotal">
								</el-pagination>
							</div>
						</el-card>
					</div>
				</el-tab-pane>

				<!-- 文件Hash管理 -->
				<el-tab-pane label="文件Hash管理" name="4">
					<div style="margin: 1% 10% ; height: 50rem;">
						<el-card shadow="always" style="height: 100%;">
							<el-input placeholder="请输入Appid" v-model="filehashappidinput"
								style="width: 20%;"></el-input>
							<el-input placeholder="请输入文件名" v-model="filehashmoudlenameinput"
								style="width: 20%; margin-left: 1%;"></el-input>
							<el-input placeholder="请输入版本" v-model="filehashversioninput"
								style="width: 20%; margin-left: 1%;"></el-input>
							<el-button icon="el-icon-search" style="margin-left: 5%;" type="primary"
								@click="searchfilehash">搜索</el-button>
							<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary"
								@click="addfilehashVisiable = true">添加新文件Hash记录</el-button>
							<el-dialog title="添加新文件Hash记录" :visible.sync="addfilehashVisiable" width="30%"
								style="margin-top: 5%;" @close="closeAddFileHash">
								<span>Appid : </span>
								<el-input v-model="addfilehashappidInput" placeholder="请输入Appid"
									style="width: 50%;margin-left: 16%;"></el-input>
								<br>
								<br>
								<span>文件名 : </span>
								<el-input v-model="addfilehashmoudlenameInput" placeholder="请输入文件名"
									style="width: 50%;margin-left: 15%;"></el-input>
								<br>
								<br>
								<span>版本 : </span>
								<el-input v-model="addfilehashversionInput" placeholder="请输入版本"
									style="width: 50%;margin-left: 18%;"></el-input>
								<br>
								<br>
								<span>文件Hash : </span>
								<el-input v-model="addfilehashhashcodeInput" placeholder="请输入文件Hash"
									style="width: 50%;margin-left: 11%;"></el-input>
								<br>
								<br>
								<span>是否启用 : </span>
								<el-switch v-model="addfilehashisopenInput" :active-value="true" :inactive-value="false" style="margin-left: 2%;"></el-switch>
								<br>
								<br>
								<span slot="footer" class="dialog-footer">
									<el-button @click="addfilehashVisiable = false">取 消</el-button>
									<el-button icon="el-icon-circle-plus-outline" style="margin-left: 5%;" type="primary" @click="addFileHash">添 加</el-button>
								</span>
							</el-dialog>
							<!-- 修改文件Hash -->
							<el-dialog title="修改文件Hash" :visible.sync="changefilehashVisiable" width="50%"
								style="margin-top: 5%;" @close="closeChangeFileHash">
								<br>
								<span>新文件Hash : </span>
								<el-input v-model="changefilehashhashcodeInput" placeholder="请输入文件Hash"
									style="width: 70%;"></el-input>
								<br>
								<br>
								<span slot="footer" class="dialog-footer">
									<el-button @click="changefilehashVisiable = false">取 消</el-button>
									<el-button style="margin-left: 5%;" type="primary" @click="changeFileHashcode">修
										改</el-button>
								</span>
							</el-dialog>

							<br>
							<br>
							<el-table :data="filehashcurrentPageData" border style="width: 100%;" height="35rem">
								<el-table-column prop="appid" label="Appid" width="80">
								</el-table-column>
								<el-table-column prop="moudlename" label="文件名" width="200">
								</el-table-column>
								<el-table-column prop="version" label="版本" width="100">
								</el-table-column>
								<el-table-column prop="hashcode" label="文件Hash" width="560">
								</el-table-column>
								<el-table-column label="是否启用" width="120">
									<template v-slot="scope">
										<el-switch v-model="scope.row.isopen" :active-value="true" :inactive-value="false" @change="changeFileHashOpen(scope.row.appid, scope.row.moudlename, scope.row.version, scope.row.isopen)"></el-switch>
									</template>
								</el-table-column>
								<el-table-column label="操作" width="220">
									<template v-slot="scope">
										<el-button type="danger" size="small" @click="deleteFileHashShow(scope.row.appid, scope.row.moudlename, scope.row.version)">删除记录</el-button>
										<el-button type="warning" size="small" @click="changeFileHashShow(scope.row.appid, scope.row.moudlename, scope.row.version, scope.row.isopen)">修改Hash</el-button>
									</template>
								</el-table-column>
							</el-table>
							<div class="sense-table-pager" v-if="filehashtotal > 0">
								<el-pagination background @size-change="filehashhandleSizeChange"
									@current-change="filehashhandleCurrentChange" :current-page="filehashcurrentPage"
									:page-sizes="[10, 20, 30]" :page-size="filehashpagesize"
									layout="total, sizes, prev, pager, next, jumper" :total="filehashtotal">
								</el-pagination>
							</div>
						</el-card>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script>
import apiClient from '../api/axios';


export default {
	data() {
		return {
			// 总数
			companytotal: 0, // 公司数据
			iptotal: 0,	// ip数据
			wifitotal: 0, // wifi数据
			domaintotal: 0, // 域名数据
			guidtotal: 0, // guid数据
			chipsntotal: 0, // 芯片号数据
			casesntotal: 0, // 外壳号数据
			ahxidtotal: 0, // ahxid数据
			apptotal: 0, // app数据
			filehashtotal: 0, // 文件Hash数据

			// 当前页
			comapnycurrentPage: 1, // 公司数据
			ipcurpage: 1, // ip数据
			wificurpage: 1, // wifi数据
			domaincurpage: 1, // 域名数据
			guidcurrentPage: 1, // guid数据
			chipsncurrentPage: 1, // 芯片号数据
			casesncurrentPage: 1, // 外壳号数据
			ahxidcurrentPage: 1, // ahxid数据
			appcurrentPage: 1, // app数据
			filehashcurrentPage: 1, // 文件Hash数据
			// 每页显示条数
			companypageSize: 10, // 公司数据
			ippagesize: 10, // ip数据
			wifipagesize: 10, // wifi数据
			domainpagesize: 10, // 域名数据
			guidpagesize: 10, // guid数据
			chipsnpagesize: 10, // 芯片号数据
			casesnpagesize: 10, // 外壳号数据
			ahxidpagesize: 10, // ahxid数据
			apppagesize: 10, // app数据
			filehashpagesize: 10, // 文件Hash数据
			// 对话框
			addcompanyvisiable: false,
			changecompanynamevisiable: false,
			addipvisiable: false,
			addwifivisiable: false,
			addDomainVisiable: false,
			addguidVisiable: false,
			addchipsnVisiable: false,
			addcasesnVisiable: false,
			addahxidVisiable: false,
			addappVisiable: false,
			changeguidremarkvisiable: false,
			changechipsnremarkvisiable: false,
			changecasesnremarkvisiable: false,
			changeahxidremarkvisiable: false,
			changehashVisiable: false,
			changeipvisiable: false,
			changewifivisiable: false,
			changeDomainVisiable: false,
			changeappVisiable: false,
			addfilehashVisiable: false,
			changefilehashVisiable: false,
			changefilehashhashcodeInput: '',
			// 输入框
			addcompanyname: '',
			changecomname: '',
			addipinput: '',
			addwifiinput: '',
			addDomainInput: '',
			addguidInput: '',
			addchipsnInput: '',
			addcasesnInput: '',
			addahxidInput: '',
			addappidInput: '',
			addappnameInput: '',
			addguidremarkInput: '',
			addchipsnremarkInput: '',
			addcasesnremarkInput: '',
			addahxidremarkInput: '',
			changeguidremarkinput: '',
			changechipsnremarkinput: '',
			changecasesnremarkinput: '',
			changeahxidremarkinput: '',
			changeappremarkInput: '',
			changehashinput: '',
			addhashmodeInput: '',
			filehashappidinput: '',
			filehashmoudlenameinput: '',
			filehashversioninput: '',
			addfilehashappidInput: '',
			addfilehashmoudlenameInput: '',
			addfilehashversionInput: '',
			addfilehashhashcodeInput: '',
			addfilehashisopenInput: false,
			curfilehashmoudlename: '',
			curfilehashversion: '',
			curfilehashhashcode: '',
			curfilehashisopen: false,
			curfilehashappid: '',
			
			// 当前选择数据
			curcompanyid: '',
			curip: '',
			curwifi: '',
			curdomain: '',
			curcompanyname: '',
			curguid: '',
			curchipsn: '',
			curcasesn: '',
			curahxid: 0,
			appid: '',
			curappname: '',
			curappid: '',


			// 公司列表
			selectcompanyid: '',
			companylist: [],

			curcompanyid: '',
			tabsActive: '1',
			companyactive: '1',
			blacklisttabActive: '1',
			// 表格数据
			tableData: [],
			companydata: [],
			iptabledata: [],
			wifitabledata: [],
			domainTableData: [],
			guidtabeldata: [],
			chipsntabledata: [],
			casesntabledata: [],
			ahxidtabledata: [],
			apptabledata: [],
			filehashtabledata: [],
			// 搜索输入框
			companyinput: '',
			ipinput: '',
			wifiinput: '',
			domaininput: '',
			guidinput: '',
			chipsninput: '',
			casesninput: '',
			ahxidinput: '',
			appinput: '',
		}
	},
	mounted() {
		this.getCompanyData();
		this.getGuidData();
		this.getAppData();
		this.getFileHashData();
	},

	computed: {
		// 公司数据
		companycurrentPageData() {
			const start = (this.comapnycurrentPage - 1) * this.companypageSize;
			const end = start + this.companypageSize;
			return this.companydata.slice(start, end);
		},
		// ip数据
		ipcurrentPageData() {
			const start = (this.ipcurpage - 1) * this.ippagesize;
			const end = start + this.ippagesize;
			return this.iptabledata.slice(start, end);
		},
		// wifi数据
		wificurrentPageData() {
			const start = (this.wificurpage - 1) * this.wifipagesize;
			const end = start + this.wifipagesize;
			return this.wifitabledata.slice(start, end);
		},
		// 域名数据
		domaincurrentPageData() {
			const start = (this.domaincurpage - 1) * this.domainpagesize;
			const end = start + this.domainpagesize;
			return this.domainTableData.slice(start, end);
		},
		// guid数据
		guidcurrentPageData() {
			const start = (this.guidcurrentPage - 1) * this.guidpagesize;
			const end = start + this.guidpagesize;
			return this.guidtabeldata.slice(start, end);
		},
		// 芯片号数据
		chipsncurrentPageData() {
			const start = (this.chipsncurrentPage - 1) * this.chipsnpagesize;
			const end = start + this.chipsnpagesize;
			return this.chipsntabledata.slice(start, end);
		},
		// 外壳号数据
		casesncurrentPageData() {
			const start = (this.casesncurrentPage - 1) * this.casesnpagesize;
			const end = start + this.casesnpagesize;
			return this.casesntabledata.slice(start, end);
		},

		// ahxid数据
		ahxidcurrentPageData() {
			const start = (this.ahxidcurrentPage - 1) * this.ahxidpagesize;
			const end = start + this.ahxidpagesize;
			return this.ahxidtabledata.slice(start, end);
		},

		// app数据
		appcurrentPageData() {
			const start = (this.appcurrentPage - 1) * this.apppagesize;
			const end = start + this.apppagesize;
			return this.apptabledata.slice(start, end);
		},

		// 文件Hash数据
		filehashcurrentPageData() {
			const start = (this.filehashcurrentPage - 1) * this.filehashpagesize;
			const end = start + this.filehashpagesize;
			return this.filehashtabledata.slice(start, end);
		},
	},
	methods: {
		handleClick() {
			console.log(this.tabsActive);
		},
		goback() {
			this.$router.go(-1);
		},

		// 公司标签转换
		companyClick() {
			if (this.companyactive == '1') {
				this.companyinput = '';
				this.getCompanyData();
			} else if (this.companyactive == '2') {
				this.ipinput = '';
				this.getIpData();
			} else if (this.companyactive == '3') {
				this.wifiinput = '';
				this.getWifiData();
			} else if (this.companyactive == '4') {
				this.domaininput = '';
				this.getDomainData();
			}
		},

		// 黑名单标签转换
		blacklisthandleTabClick() {
			if (this.blacklisttabActive == '1') {
				this.guidinput = '';
				this.getGuidData();
			} else if (this.blacklisttabActive == '2') {
				this.chipsninput = '';
				this.getChipsnData();
			} else if (this.blacklisttabActive == '3') {
				this.casesninput = '';
				this.getCasesnData();
			} else if (this.blacklisttabActive == '4') {
				this.ahxidinput = '';
				this.getAhxidData();
			}
		},

		// 获取公司数据
		getCompanyData() {
			let url = '/getallcompanynames';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.companydata = [];
						this.companytotal = 0;
					} else {
						this.companydata = res.data.data;
						this.companytotal = res.data.data.length;
					}
				} else {
					this.$message.error(res.data.msg);
				}
			});
		},

		// 获取ip数据
		getIpData() {
			let url = '/getallipmaps';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.iptabledata = [];
						this.iptotal = 0;
					} else {
						this.iptabledata = res.data.data;
						this.iptotal = res.data.data.length;
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取wifi数据
		getWifiData() {
			let url = '/getallwifiroutermaps';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.wifitabledata = [];
						this.wifitotal = 0;
					} else {
						this.wifitabledata = res.data.data;
						this.wifitotal = res.data.data.length;
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取域名数据
		getDomainData() {
			let url = '/getalldomainmaps';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.domainTableData = [];
						this.domaintotal = 0;
					} else {
						this.domainTableData = res.data.data;
						this.domaintotal = res.data.data.length;
					}
				} else {
					this.$message.error(res.data.msg);
				}
			});
		},

		// 获取guid数据
		getGuidData() {
			let url = '/getblacklist_guid';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.guidtabeldata = [];
						this.guidtotal = 0;
					} else {
						this.guidtabeldata = res.data.data;
						this.guidtotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取芯片号数据
		getChipsnData() {
			let url = '/getblacklist_chipsn';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.chipsntabledata = [];
						this.chipsntotal = 0;
					} else {
						this.chipsntabledata = res.data.data;
						this.chipsntotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取外壳号数据
		getCasesnData() {
			let url = '/getblacklist_casesn';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.casesntabledata = [];
						this.casestotal = 0;
					} else {
						this.casesntabledata = res.data.data;
						this.casestotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取ahxid数据
		getAhxidData() {
			let url = '/getblacklist_ahxid';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.ahxidtabledata = [];
						this.ahxidtotal = 0;
					} else {
						this.ahxidtabledata = res.data.data;
						this.ahxidtotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取app数据
		getAppData() {
			let url = '/getallappid2name';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.apptabledata = [];
						this.apptotal = 0;
					} else {
						this.apptabledata = res.data.data;
						this.apptotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取文件Hash数据
		getFileHashData() {
			let url = '/getallappmoudlehash';
			apiClient.get(url).then((res) => {
				if (res.data.code == 0) {
					if (res.data.data == null) {
						this.filehashtabledata = [];
						this.filehashtotal = 0;
					} else {
						this.filehashtabledata = res.data.data;
						this.filehashtotal = res.data.data.length;
					}
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},


		// 添加新公司名称
		addCompanyName() {
			let url = '/addcompanyname';
			let param = {
				companyname: this.addcompanyname
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addcompanyvisiable = false;
					this.getCompanyData();
				} else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 获取所有公司列表
		getAllCompanyList() {
			// 获取所有公司名称
			apiClient.get('/getallcompanynames').then(response => {
				this.companylist = response.data.data;
			}).catch(error => {
				this.$message({
					message: error,
					type: 'warning'
				});
			});
		},

		// 添加ip显示对话框
		addIPcompanyvisiable() {
			this.addipvisiable = true;
			this.getAllCompanyList();
		},

		// 添加wifi显示对话框
		addWIFIcompanyvisiable() {
			this.addwifivisiable = true;
			this.getAllCompanyList();
		},

		// 添加域名显示对话框
		addDomaincompanyvisiable() {
			this.addDomainVisiable = true;
			this.getAllCompanyList();
		},

		// 添加app显示对话框
		addAppcompanyvisiable() {
			this.addappVisiable = true;
		},

		// 添加ip
		addIPcompany() {
			if (!this.isValidIPv4(this.addipinput)) {
				this.$message.error("请输入正确的IP地址");
				return;
			}
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/markcompanyip';
			let param = {
				"ip": this.addipinput,
				"companyid": this.selectcompanyid
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addipvisiable = false;
					this.getIpData();
				} else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 添加wifi
		addWIFIcompany() {
			if (!this.isValidMAC(this.addwifiinput)) {
				this.$message.error("请输入正确的MAC地址");
				return;
			}
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/markcompanywifirouter';
			let param = {
				"wifirouter": this.addwifiinput,
				"companyid": this.selectcompanyid
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addwifivisiable = false;
					this.getWifiData();
				} else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},
		// 校验域名格式
		isValidDomain(str) {
			const regex = /^(?!:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/;
			return regex.test(str);
		},
		// 添加域名
		addDomain() {
			if (!this.isValidDomain(this.addDomainInput)) {
				this.$message.error("请输入正确的域名");
				return;
			}
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/markcompanydomain';
			let param = {
				"domain": this.addDomainInput,
				"companyid": this.selectcompanyid
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addDomainVisiable = false;
					this.getDomainData();
				} else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			});
		},

		// 添加guid
		addGuid() {
			if (this.addguidInput == '') {
				this.$message.error("请输入GUID");
				return;
			}
			let url = '/addblacklist_guid';
			let param = {
				"guid": this.addguidInput,
				"remark": this.addguidremarkInput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addguidVisiable = false;
					this.getGuidData();
				} else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			});
		},

		// 添加芯片号
		addChipsn() {
			if (this.addchipsnInput == '') {
				this.$message.error("请输入芯片号");
				return;
			}
			let url = '/addblacklist_chipsn';
			let param = {
				"chipsnhex": this.addchipsnInput,
				"remark": this.addchipsnremarkInput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addchipsnVisiable = false;
					this.getChipsnData();
				}
				else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 添加外壳号
		addCasesn() {
			if (this.addcasesnInput == '') {
				this.$message.error("请输入外壳号");
				return;
			}
			let url = '/addblacklist_casesn';
			let param = {
				"casesn": this.addcasesnInput,
				"remark": this.addcasesnremarkInput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addcasesnVisiable = false;
					this.getCasesnData();
				}
				else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 添加ahxid
		addAhxid() {
			if (this.addahxidInput == '') {
				this.$message.error("请输入ahxid");
				return;
			}
			let url = '/addblacklist_ahxid';
			let param = {
				"ahxid": Number(this.addahxidInput),
				"remark": this.addahxidremarkInput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addahxidVisiable = false;
					this.getAhxidData();
				}
				else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 添加app
		addApp() {
			if (this.addappidInput == '') {
				this.$message.error("请输入appid");
				return;
			}
			if (this.addappnameInput == '') {
				this.$message.error("请输入app名称");
				return;
			}
			if (this.addhashmodeInput == '') {
				this.$message.error("请选择文件校验模式");
				return;
			}
			let url = '/storeappid2name';
			let param = {
				"appid": this.addappidInput,
				"appname": this.addappnameInput,
				"hashmode": Number(this.addhashmodeInput)
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addappVisiable = false;
					this.getAppData();
				}
				else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 添加文件Hash
		addFileHash() {
			if (this.addfilehashappidInput == '') {
				this.$message.error("请输入Appid");
				return;
			}
			if (this.addfilehashmoudlenameInput == '') {
				this.$message.error("请输入文件名");
				return;
			}
			if (this.addfilehashversionInput == '') {
				this.$message.error("请输入版本");
				return;
			}
			if (this.addfilehashhashcodeInput == '') {
				this.$message.error("请输入文件Hash");
				return;
			}
			let url = '/storeappmoudlehash';
			let param = {
				"appid": this.addfilehashappidInput,
				"moudlename": this.addfilehashmoudlenameInput,
				"version": this.addfilehashversionInput,
				"hashcode": this.addfilehashhashcodeInput,
				"isopen": this.addfilehashisopenInput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.addfilehashVisiable = false;
					this.getFileHashData();
				}
				else {
					this.$message.error("添加失败:" + res.data.msg);
				}
			})
		},

		// 修改ip显示对话框
		changeIPshow(row) {
			this.getAllCompanyList();
			this.curip = row.ip;
			this.curcompanyid = row.companyid;
			this.curcompanyname = row.companyname;
			this.changeipvisiable = true;
		},

		// 修改wifi显示对话框
		changeWIFIshow(row) {
			this.getAllCompanyList();
			this.curwifi = row.wifirouter;
			this.curcompanyid = row.companyid;
			this.curcompanyname = row.companyname;
			this.changewifivisiable = true;
		},
		// 修改域名显示对话框
		changeDomainShow(row) {
			this.getAllCompanyList();
			this.curdomain = row.domain;
			this.curcompanyid = row.companyid;
			this.curcompanyname = row.companyname;
			this.changeDomainVisiable = true;
		},

		// 修改guid显示对话框
		changeremarkvisiable(guid) {
			this.curguid = guid;
			this.changeguidremarkvisiable = true;
		},

		// 修改芯片号显示对话框
		changeChipsnshow(chipsn) {
			this.curchipsn = chipsn;
			this.changechipsnremarkvisiable = true;
		},

		// 修改外壳号显示对话框
		changeCasesnshow(casesn) {
			this.curcasesn = casesn;
			this.changecasesnremarkvisiable = true;
		},

		// 修改ahxid显示对话框
		changeAhxidshow(ahxid) {
			this.curahxid = ahxid;
			this.changeahxidremarkvisiable = true;
		},

		// 修改hash显示对话框
		changeHashShow(appid) {
			this.curappid = appid;
			this.changehashVisiable = true;
		},

		// 修改app显示对话框
		changeAppshow(appid, appname) {
			this.curappname = appname;
			this.curappid = appid;
			this.changeappVisiable = true;
		},

		// 修改文件Hash显示对话框
		changeFileHashShow(appid, moudlename, version, isopen) {
			this.curfilehashappid = String(appid);
			this.curfilehashmoudlename = moudlename;
			this.curfilehashversion = version;
			this.curfilehashisopen = isopen;
			this.changefilehashVisiable = true;
		},

		// 修改ip
		changeIPcompany() {
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/updateipmap';
			let param = {
				"ip": this.curip,
				"companyid": this.selectcompanyid
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changeipvisiable = false;
					this.getIpData();
				} else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改wifi
		changeWIFIcompany() {
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/updatewifiroutermap';
			let param = {
				"wifi": this.curwifi,
				"companyid": this.selectcompanyid
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changewifivisiable = false;
					this.getWifiData();
				} else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},
		// 修改域名
		changeDomain() {
			if (this.selectcompanyid == '') {
				this.$message.error("请选择公司名称");
				return;
			}
			let url = '/updatedomainmap';
			let param = {
				"domain": this.curdomain,
				"companyid": this.selectcompanyid
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changeDomainVisiable = false;
					this.getDomainData();
				} else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			});
		},

		// 修改guid
		changeGuid() {
			let url = '/modifyblacklist_guidremark';
			let param = {
				"guid": this.curguid,
				"remark": this.changeguidremarkinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changeguidremarkvisiable = false;
					this.getGuidData();
				} else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改芯片号
		changeChipsn() {
			let url = '/modifyblacklist_chipsnremark';
			let param = {
				"chipsnhex": this.curchipsn,
				"remark": this.changechipsnremarkinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changechipsnremarkvisiable = false;
					this.getChipsnData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改外壳号
		changeCasesn() {
			let url = '/modifyblacklist_casesnremark';
			let param = {
				"casesn": this.curcasesn,
				"remark": this.changecasesnremarkinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changecasesnremarkvisiable = false;
					this.getCasesnData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改ahxid
		changeAhxid() {
			let url = '/modifyblacklist_ahxidremark';
			let param = {
				"ahxid": Number(this.curahxid),
				"remark": this.changeahxidremarkinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changeahxidremarkvisiable = false;
					this.getAhxidData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		//  修改app
		changeApp() {
			let url = '/updateappnamebyappid';
			let param = {
				"appid": this.curappid,
				"appname": this.changeappremarkInput
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changeappnamevisiable = false;
					this.getAppData();
				} else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改Hash模式
		changeHashMode() {
			let url = '/updateapphashmodebyappid';
			let param = {
				"appid": String(this.curappid),
				"hashmode": Number(this.changehashinput)
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changehashVisiable = false;
					this.getAppData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改文件Hash启用状态
		changeFileHashOpen(appid, moudlename, version, isopen) {
			let url = '/updateappmoudlehashstatus';
			let param = {
				"appid": String(appid),
				"moudlename": moudlename,
				"version": version,
				"isopen": isopen
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.getFileHashData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 修改文件Hashcode
		changeFileHashcode() {
			let url = '/updateappmoudlehash';
			let param = {
				"appid": String(this.curfilehashappid),
				"moudlename": this.curfilehashmoudlename,
				"version": this.curfilehashversion,
				"hashcode": this.changefilehashhashcodeInput,
				"isopen": this.curfilehashisopen
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success("更新成功");
					this.changefilehashVisiable = false;
					this.getFileHashData();
				}
				else {
					this.$message.error("更新失败:" + res.data.msg);
				}
			})
		},

		// 校验ipv4格式
		isValidIPv4(str) {
			const regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
			return regex.test(str);
		},

		// 校验mac地址格式
		isValidMAC(str) {
			const regex = /^([0-9A-Fa-f]{2}[:.-]?){5}[0-9A-Fa-f]{2}$/;
			return regex.test(str);
		},

		// 搜索公司名称
		searchcompany() {
			let url = '/searchcompanyname';
			let param = {
				companyname: this.companyinput
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.companydata = res.data.data;
					this.companytotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error(res.data.msg);
				}
			});
		},

		// 搜索ip
		searchip() {
			let url = '/searchip';
			let param = {
				"ip": this.ipinput
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.iptabledata = res.data.data;
					this.iptotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 搜索wifi
		searchwifi() {
			let url = '/searchwifi';
			let param = {
				"wifirouter": this.wifiinput
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.wifitabledata = res.data.data;
					this.wifitotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 搜索域名
		searchdomain() {
			let url = '/searchdomain';
			let param = {
				"domain": this.domaininput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.domainTableData = res.data.data;
					this.domaintotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error(res.data.msg);
				}
			});
		},

		// 搜索guid
		searchguid() {
			let url = '/searchblacklist_guid';
			let param = {
				"guid": this.guidinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.guidtabeldata = res.data.data;
					this.guidtotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error(res.data.msg);
				}
			});
		},

		// 搜索芯片号
		searchchipsn() {
			let url = '/searchblacklist_chipsn';
			let param = {
				"chipsnhex": this.chipsninput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.chipsntabledata = res.data.data;
					this.chipsntotal = res.data.data.length;
					this.$message.success("搜索成功");
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 搜索外壳号
		searchcasesn() {
			let url = '/searchblacklist_casesn';
			let param = {
				"casesn": this.casesninput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.casesntabledata = res.data.data;
					this.casesntotal = res.data.data.length;
					this.$message.success("搜索成功");
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 搜索ahxid
		searchahxid() {
			if (this.ahxidinput == '') {
				this.getAhxidData();
				this.$message.success("搜索成功");
				return;
			}
			let url = '/searchblacklist_ahxid';
			let param = {
				"ahxid": Number(this.ahxidinput)
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.ahxidtabledata = res.data.data;
					this.ahxidtotal = res.data.data.length;
					this.$message.success("搜索成功");
				}
				else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 搜索app
		searchapp() {
			let url = '/searchappname';
			let param = {
				"appname": this.appinput
			};
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.apptabledata = res.data.data;
					this.apptotal = res.data.data.length;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 关闭添加公司对话框
		closeaddcompany() {
			this.addcompanyname = '';
		},

		// 关闭修改公司对话框
		closechangecompany() {
			this.changecomname = '';
		},

		// 关闭添加ip对话框
		closeaddip() {
			this.addipinput = '';
			this.selectcompanyid = '';
		},

		// 关闭添加wifi对话框
		closeaddwifi() {
			this.addwifiinput = '';
			this.selectcompanyid = '';
		},

		// 关闭添加域名对话框
		closeAddDomain() {
			this.addDomainInput = '';
			this.selectcompanyid = '';
		},

		// 关闭添加guid对话框
		closeAddGuid() {
			this.addGuidInput = '';
		},

		// 关闭添加芯片号对话框
		closeAddChipsn() {
			this.addchipsnInput = '';
		},

		// 关闭添加外壳号对话框
		closeAddCasesn() {
			this.addCasesnInput = '';
		},

		// 关闭添加ahxid对话框
		closeAddAhxid() {
			this.addAhxidInput = '';
		},

		// 关闭添加app对话框
		closeAddApp() {
			this.addAppInput = '';
		},

		// 关闭修改ip对话框
		closeChangeIP() {
			this.selectcompanyid = '';
		},

		// 关闭修改wifi对话框
		closeChangeWIFI() {
			this.selectcompanyid = '';
		},

		// 关闭修改域名对话框
		closeChangeDomain() {
			this.selectcompanyid = '';
		},

		// 关闭修改guid对话框
		closechangeremark() {
			this.changeguidremarkinput = '';
		},

		// 关闭修改芯片号对话框
		closeChangeChipsn() {
			this.changechipsnremarkinput = '';
		},

		// 关闭修改外壳号对话框
		closeChangeCasesn() {
			this.changecasesnremarkinput = '';
		},

		// 关闭修改ahxid对话框
		closeChangeAhxid() {
			this.changeahxidremarkinput = '';
		},

		// 关闭修改app对话框
		closeChangeApp() {
			this.curappname = '';
			this.curappid = '';
			this.changeappremarkInput = '';
		},

		// 关闭app修改hash模式对话框
		closeChangeHash() {
			this.curappid = '';
			this.changehashinput = '';
		},

		// 关闭添加文件hash对话框
		closeAddFileHash() {
			this.addfilehashappid = '';
			this.addfilehashmoudlename = '';
			this.addfilehashversion = '';
			this.addfilehashisopen = '';
			this.addfilehashhashcode = '';
		},

		// 关闭修改文件hash对话框
		closeChangeFileHash() {
			this.curfilehashappid = '';
			this.curfilehashmoudlename = '';
			this.curfilehashversion = '';
			this.curfilehashisopen = '';
			this.changefilehashhashcodeInput = '';
		},



		// 修改公司名称
		changecomnamevisiable(val) {
			this.curcompanyid = val;
			this.changecompanynamevisiable = true;
		},

		// 修改公司名称
		changeCompanyName() {
			let url = '/updatecompanyname';
			let param = {
				"companyid": this.curcompanyid,
				"companyname": this.changecomname
			}
			apiClient.post(url, param).then((res) => {
				if (res.data.code == 0) {
					this.$message.success(res.data.msg);
					this.changecompanynamevisiable = false;
					this.getCompanyData();
				} else {
					this.$message.error("修改失败:" + res.data.msg);
				}
			})
		},

		// 删除公司名称
		opendeletecompanymessage(val) {
			this.curcompanyid = val;
			this.$confirm('确认删除此公司记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url1 = '/checkcompanyid';
				let param = {
					"companyid": this.curcompanyid,
				}
				apiClient.post(url1, param).then((res) => {
					if (res.data.code == 0) {
						if (res.data.has == true) {
							this.$message.error("该公司存在关联ip\wifi\域名,请先删除关联数据");
							return;
						} else {
							let url = '/deletecompanyname';
							apiClient.post(url, param).then((res) => {
								if (res.data.code == 0) {
									this.$message.success("删除成功");
									this.getCompanyData();
								} else {
									this.$message.error("删除失败:" + res.data.msg);
								}
							})
						}
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				})


			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除ip
		deleteIPshow(ip) {
			this.$confirm('确认删除此IP记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/deleteipmap';
				let param = {
					"ip": ip,
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getIpData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				})
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除wifi
		deleteWIFIshow(wifi) {
			this.$confirm('确认删除此Wifi记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/deletewifiroutermap';
				let param = {
					"wifirouter": wifi,
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getWifiData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				})
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},
		// 删除域名
		deleteDomainShow(domain) {
			this.$confirm('确认删除此域名记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/deletedomainmap';
				let param = {
					"domain": domain,
				};
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getDomainData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除guid
		deleteGuidShow(guid) {
			this.$confirm('确认删除此黑名单记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/removeblacklist_guid';
				let param = {
					"guid": guid,
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getGuidData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除芯片号
		deleteChipsnShow(chipsn) {
			this.$confirm('确认删除此黑名单记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/removeblacklist_chipsn';
				let param = {
					"chipsnhex": chipsn,
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getChipsnData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除外壳号
		deleteCasesnShow(casesn) {
			this.$confirm('确认删除此黑名单记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/removeblacklist_casesn';
				let param = {
					"casesn": casesn,
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getCasesnData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除ahxid
		deleteAhxidShow(ahxid) {
			this.$confirm('确认删除此黑名单记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/removeblacklist_ahxid';
				let param = {
					"ahxid": Number(ahxid),
				}
				apiClient.post(url, param).then((res) => {
					if (res.data.code == 0) {
						this.$message.success("删除成功");
						this.getAhxidData();
					} else {
						this.$message.error("删除失败:" + res.data.msg);
					}
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消删除'
				});
			});
		},

		// 删除app
		deleteAppShow(appid) {
			this.$confirm('确认删除此app记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					let url = '/deleteappid2name';
					let param = {
						"appid": appid,
					}
					apiClient.post(url, param).then((res) => {
						if (res.data.code == 0) {
							this.$message.success("删除成功");
							this.getAppData();
						} else {
							this.$message.error("删除失败:" + res.data.msg);
						}
					});
				})
		},

		// 删除文件Hash
		deleteFileHashShow(appid, moudlename, version) {
			this.$confirm('确认删除此文件Hash记录吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					let url = '/deleteappmoudlehash';
					let param = {
						"appid": String(appid),
						"moudlename": moudlename,
						"version": version
					}
					apiClient.post(url, param).then((res) => {
						if (res.data.code == 0) {
							this.$message.success("删除成功");
							this.getFileHashData();
						} else {
							this.$message.error("删除失败:" + res.data.msg);
						}
					});
				})
		},

		// 公司页大小改变
		companyhandleSizeChange(val) {
			this.companypageSize = val;
		},
		// 公司页数改变
		companyhandleCurrentChange(val) {
			this.comapnycurrentPage = val;
		},

		// IP页大小改变
		iphandleSizeChange(val) {
			this.ippageSize = val;
		},
		// IP页数改变
		iphandleCurrentChange(val) {
			this.ipcurrentPage = val;
		},

		// wifi页大小改变
		wifihandleSizeChange(val) {
			this.wifipageSize = val;
		},
		// wifi页数改变
		wifihandleCurrentChange(val) {
			this.wificurrentPage = val;
		},

		// 域名页大小改变
		domainhandleSizeChange(val) {
			this.domainpageSize = val;
		},
		// 域名页数改变
		domainhandleCurrentChange(val) {
			this.domaincurrentPage = val;
		},

		// guid页大小改变
		guidhandleSizeChange(val) {
			this.guidpageSize = val;
		},
		// guid页数改变
		guidhandleCurrentChange(val) {
			this.guidcurrentPage = val;
		},

		// 芯片号页大小改变
		chipsnhandleSizeChange(val) {
			this.chipsnpageSize = val;
		},
		// 芯片号页数改变
		chipsnhandleCurrentChange(val) {
			this.chipsncurrentPage = val;
		},

		// 外壳号页大小改变
		casesnhandleSizeChange(val) {
			this.casesnpageSize = val;
		},
		// 外壳号页数改变
		casesnhandleCurrentChange(val) {
			this.casesncurrentPage = val;
		},
		// ahxid页大小改变
		ahxidhandleSizeChange(val) {
			this.ahxidpageSize = val;
		},
		// ahxid页数改变
		ahxidhandleCurrentChange(val) {
			this.ahxidcurrentPage = val;
		},
		// app页大小改变
		apphandleSizeChange(val) {
			this.apppageSize = val;
		},
		// app页数改变
		apphandleCurrentChange(val) {
			this.appcurrentPage = val;
		},
		// 文件hash页大小改变
		filehashhandleSizeChange(val) {
			this.filehashpageSize = val;
		},
		// 文件hash页数改变
		filehashhandleCurrentChange(val) {
			this.filehashcurrentPage = val;
		},
		// 搜索文件Hash
		searchfilehash() {
			let url = '/getappmoudlehash';
			const params = {
				"appid": String(this.filehashappidinput),
				"moudlename": this.filehashmoudlenameinput,
				"version": this.filehashversioninput
			};
			apiClient.post(url, params).then((res) => {
				if (res.data.code == 0) {
					this.filehashtabledata = res.data.data;
					this.filehashtotal = res.data.data.length;
					this.$message.success("搜索成功");
				} else {
					this.$message.error("搜索失败:" + res.data.msg);
				}
			});
		},
	},

}
</script>
