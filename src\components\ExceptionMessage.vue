<template>
	<div>
		<div class="sense-main">
			<div class="sense-table-list sense-table-ellipsis">
				<el-table ref="multipleTable" @selection-change="handleSelectionChange" :cell-style="{ padding: '7px' }"
					:data="currentPageData" border>
					<el-table-column type="selection" width="55">
					</el-table-column>
					<el-table-column :filters="[{ text: '在线', value: 1 }, { text: '离线', value: 0 }]"
						:filter-method="filterisonline" filter-placement="bottom-end" label="在线" min-width="4%">
						<template slot-scope="scope">
							<span v-if="scope.row.isonline == 0" class="sense-mestype sense-mestype-0">离线</span>
							<span v-if="scope.row.isonline == 1" class="sense-mestype sense-mestype-5">在线</span>
						</template>
					</el-table-column>
					<el-table-column label="状态" min-width="5%"
						:filters="[{ text: '正常', value: 0 }, { text: '异常', value: 1 }, { text: '可疑', value: 2 }, { text: '盗版', value: 3 }]"
						:filter-method="filterstatus" filter-placement="bottom-end">
						<template slot-scope="scope">
							<span v-if="scope.row.status == 0" class="sense-mestype sense-mestype-5">正常</span>
							<span v-if="scope.row.status == 1" class="sense-mestype sense-mestype-4">异常</span>
							<span v-if="scope.row.status == 2" class="sense-mestype sense-mestype-3">可疑</span>
							<span v-if="scope.row.status == 3" class="sense-mestype sense-mestype-2">盗版</span>
						</template>
					</el-table-column>
					<el-table-column prop="addinfo.host_fqdn" label="终端名称FQDN" min-width="8%"
						show-overflow-tooltip></el-table-column>
					<el-table-column prop="addinfo.machine_id" label="机器ID(Macine-GUID)" min-width="10%"
						show-overflow-tooltip></el-table-column>
					<el-table-column prop="addinfo.internet_ipv4" label="IPV4" min-width="6%"
						show-overflow-tooltip></el-table-column>
					<el-table-column prop="addinfo.host_mac" label="MAC地址" min-width="8%"
						show-overflow-tooltip></el-table-column>
					<el-table-column prop="city" label="省/市" min-width="5%" show-overflow-tooltip></el-table-column>
					<el-table-column label="软件信息" min-width="7%">
						<template slot-scope="scope">
							<el-button @click="dialogClick(scope.row)" type="text">{{
								scope.row.addinfo.app_name }}</el-button>
							<el-dialog title="详细信息" :visible.sync="dialogFormVisible">
								<el-descriptions :column=1>
									<el-descriptions-item label="Comments">{{ Dialogdata.Comments
										}}</el-descriptions-item>
									<el-descriptions-item label="CompanyName">{{ Dialogdata.CompanyName
										}}</el-descriptions-item>
									<el-descriptions-item label="FileVersion">{{ Dialogdata.FileVersion
										}}</el-descriptions-item>
									<el-descriptions-item label="InternalName">{{ Dialogdata.InternalName
										}}</el-descriptions-item>
									<el-descriptions-item label="LegalCopyright">{{ Dialogdata.LegalCopyright
										}}</el-descriptions-item>
									<el-descriptions-item label="LegalTrademarks">{{ Dialogdata.LegalTrademarks
										}}</el-descriptions-item>
									<el-descriptions-item label="OriginalFilename">{{ Dialogdata.OriginalFilename
										}}</el-descriptions-item>
									<el-descriptions-item label="PrivateBuild">{{ Dialogdata.PrivateBuild
										}}</el-descriptions-item>
									<el-descriptions-item label="ProductName">{{ Dialogdata.ProductName
										}}</el-descriptions-item>
									<el-descriptions-item label="ProductVersion">{{ Dialogdata.ProductVersion
										}}</el-descriptions-item>
									<el-descriptions-item label="SpecialBuild">{{ Dialogdata.SpecialBuild
										}}</el-descriptions-item>
								</el-descriptions>
								<div slot="footer" class="dialog-footer">
									<el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
								</div>
							</el-dialog>
						</template>
					</el-table-column>
					<el-table-column prop="detialinfo" label="授权检测结果" min-width="9%"
						show-overflow-tooltip></el-table-column>
					<el-table-column label="最后上线时间" min-width="7%" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="scope.row.isonline == 0" class="sense-mestype sense-mestype-0">{{
								scope.row.lastOnlineTime }}</span>
							<span v-if="scope.row.isonline == 1" class="sense-mestype sense-mestype-5">在线(now)</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" min-width="7%" show-overflow-tooltip>
						<template slot-scope="scope">
							<router-link v-if="scope.row.isonline == 1" :to="{ path: '/execCounter' }">
								<el-button size="mini" @click="handleClick(scope.row)">执行反制</el-button>
							</router-link>
							<router-link v-if="scope.row.isonline == 0" :to="{ path: '/execCounteroffline' }">
								<el-button size="mini" @click="handleClick(scope.row)">执行反制</el-button>
							</router-link>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="sense-table-pager" v-if="total > 0">
				<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:current-page="currentPage" :page-sizes="[10, 20, 30]" :page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</div>
		<div class="all-dialog">

		</div>
	</div>
</template>

<script>
import apiClient from '../api/axios'
export default {
	data() {
		return {
			dialogFormVisible: false,
			isPreview: '',
			previewObj: {
				exceptionListData: [
					{ 'isonLine': '0', 'status': '0', 'FQDN': 'DESKTOP-L329935', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': 'Master4.3', 'detialinfo': '检测到WINWORD.EXE的反黑启动失败，返回:0x102', 'lastlogin': '在线(now)' },
					{ 'isonLine': '1', 'status': '3', 'FQDN': 'DESKTOP-*********', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '上海-上海', 'softinfo': 'VisualMaster4.3', 'detialinfo': '检测到WINWORD.EXE的EnumDevice函数被挂钩', 'lastlogin': '在线(now)' },
					{ 'isonLine': '1', 'status': '2', 'FQDN': 'DESKTOP-L32254', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': '未上传', 'detialinfo': '检测到高仿精锐4', 'lastlogin': '2024-02-14 08:50:11' },
					{ 'isonLine': '0', 'status': '3', 'FQDN': 'DESKTOP-L322995', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '广东-广州', 'softinfo': 'Visual4.3', 'detialinfo': '风控函数运行失败 ERRORCODE= 0x05', 'lastlogin': '在线(now)' },
					{ 'isonLine': '1', 'status': '1', 'FQDN': 'DESKTOP-L3299455', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': 'VisualMaster4.3', 'detialinfo': '检测到EXCEL.EXE的EnumDevice函数被挂钩', 'lastlogin': '在线(now)' },
					{ 'isonLine': '0', 'status': '1', 'FQDN': 'DESKTOP-L3299235', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '浙江-杭州', 'softinfo': 'VisMaster4.3', 'detialinfo': '检测到WINWORD.EXE的反黑启动失败，返回:0x102', 'lastlogin': '在线(now)' },
					{ 'isonLine': '0', 'status': '3', 'FQDN': 'DESKTOP-L322456995', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': '未上传', 'detialinfo': '检测到破解软件 ，特征 {劫持+替换锁}', 'lastlogin': '2024-02-14 08:50:11' },
					{ 'isonLine': '1', 'status': '2', 'FQDN': 'DESKTOP-L329925', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': 'VisualMaster4.3', 'detialinfo': '检测到EXCEL.EXE的EnumDevice函数被挂钩', 'lastlogin': '在线(now)' },
					{ 'isonLine': '0', 'status': '1', 'FQDN': 'DESKTOP-L32295', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '深圳-深圳', 'softinfo': 'APPID=5 ', 'detialinfo': '检测到EXCEL.EXE的EnumDevice函数被挂钩', 'lastlogin': '2024-02-14 08:50:11' },
					{ 'isonLine': '1', 'status': '3', 'FQDN': 'DESKTOP-L32567995', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': 'Visuater4.3', 'detialinfo': '检测到EXCEL.EXE的EnumDevice函数被挂钩', 'lastlogin': '在线(now)' },
					{ 'isonLine': '0', 'status': '0', 'FQDN': 'DESKTOP-L329935', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': 'VisualMaster4.3', 'detialinfo': '检测到WINWORD.EXE的反黑启动失败，返回:0x102', 'lastlogin': '在线(now)' },
					{ 'isonLine': '1', 'status': '3', 'FQDN': 'DESKTOP-dg', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '上海-上海', 'softinfo': 'VisualMaster4.3', 'detialinfo': '检测到WINWORD.EXE的EnumDevice函数被挂钩', 'lastlogin': '在线(now)' },
					{ 'isonLine': '1', 'status': '2', 'FQDN': 'DESKTOP-dfgd', 'GUID': '10349af91a1848b69a1e160', 'IPV4': '***********', 'MAC': 'FF:EE:CC:DD:12:34:56', 'city': '北京-北京', 'softinfo': '未上传', 'detialinfo': '检测到高仿精锐4', 'lastlogin': '2024-02-14 08:50:11' },
				],
				total: 21
			},
			exceptionListData: [],  // 展示数据
			currentPage: 1,  //当前页
			pageSize: 10,  //每页条数
			total: 0,  //数据总数	
			Dialogdata: {}, // 存储app详细信息
			multipleSelection: [] // 多选数据
		}
	},
	computed: {
		currentPageData() {
			const start = (this.currentPage - 1) * this.pageSize;
			const end = start + this.pageSize;
			return this.exceptionListData.slice(start, end);
		}
	},
	methods: {
		handleSelectionChange(val) {
			this.multipleSelection = val;
		},
		// 筛选在线
		filterisonline(value, row) {
			return row.isonline === value;
		},
		// 筛选状态
		filterstatus(value, row) {
			return row.status === value;
		},
		base64ToHex(base64String) {
			// 解码 Base64 字符串为原始字符串
			const decodedString = atob(base64String);

			// 将原始字符串转换为 16 进制字符串
			let hexString = '';
			for (let i = 0; i < decodedString.length; i++) {
				// 获取每个字符的 UTF-8 编码
				const hex = decodedString.charCodeAt(i).toString(16);
				// 确保每个字节的 16 进制字符串是两位数
				hexString += ('00' + hex).slice(-2);
			}

			return hexString;
		},

		getExceptionList() {
			// 预览
			if (this.isPreview == 1) {
				this.exceptionListData = this.previewObj.exceptionListData;
				this.total = this.previewObj.total;
			} else {
				// 非预览	
			};
		},
		//格式化省市
		areaFormatter(row, index) {
			return row.province + '-' + row.city;
		},
		//切换每页显示条数
		handleSizeChange(val) {
			this.pageSize = val;
			this.getadddata();
		},
		//切换当前页
		handleCurrentChange(val) {
			this.currentPage = val;
			this.$store.commit('SET_CUR_PAGE', val);
			this.getadddata();
		},
		getadddata() {
			let url = '/getaadbypage';
			let param = {
				'page': this.currentPage,
				'limit': this.pageSize
			}
			apiClient.post(url, param).catch(error => {
				this.$message.error("server:" + error.message);
				return
			})
				.then(res => {
					if (res.data.code == 0) {
						for (let i = 0; i < res.data.results.length; i++) {
							// 数据转码
							res.data.results[i].addinfo.internet_ipv4 = atob(res.data.results[i].addinfo.internet_ipv4)
							res.data.results[i].addinfo.machine_id = this.base64ToHex(res.data.results[i].addinfo.machine_id)
							// res.data.results[i].addinfo.host_mac = atob(res.data.results[i].addinfo.host_mac)
						}
						// 获取数据
						this.exceptionListData = res.data.results;
						// 更新总条数
						this.total = res.data.results.length;
					} else {
						this.$message({
							message: "error:" + res.data.msg,
							type: 'warning'
						});
					}
				}
				)
		},
		handleClick(row) {
			this.$store.commit('SET_EXEC_cliFQDN', row.addinfo.host_fqdn);
			this.$store.commit('SET_EXEC_cliSTATUS', row.isonline);
			this.$store.commit('SET_EXEC_appNAME', row.addinfo.app_name);
			this.$store.commit('SET_EXEC_procId', row.addinfo.app_processid);
			this.$store.commit('SET_EXEC_appId', row.addinfo.app_id);
			this.$store.commit('SET_EXEC_cliMechined', row.addinfo.machine_id);
			this.$store.commit('SET_EXEC_developerIndex', row.addinfo.developer_index);
		},
		dialogClick(row) {
			this.Dialogdata = row.addinfo.app_pe_info;
			this.dialogFormVisible = true;
		}
	},
	mounted() {
		this.currentPage = this.$store.state.CurPage.curPage;
		this.getadddata();
	},
}
</script>

<style scoped lang="scss"></style>