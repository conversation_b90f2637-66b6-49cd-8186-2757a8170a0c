<template>
	<div>
		<div class="sense-main">
			<div class="sense-breadcrumb">
				<el-breadcrumb separator-class="el-icon-arrow-right">
				    <el-breadcrumb-item :to="{ path: '/piracyConfrontation' }">盗版对抗</el-breadcrumb-item>
				    <el-breadcrumb-item>反制历史记录</el-breadcrumb-item>
				</el-breadcrumb>
			</div>
			<div class="sense-search">
				<el-row>
					<el-form label-width="68px">
						<el-col :span="6">
							<el-form-item label="反制措施">
								<el-select size="medium" v-model="searchObj.cmdId">
									<el-option label="消息通知" value="1"></el-option>
									<el-option label="推送反黑数据库" value="2"></el-option>
								</el-select>
							</el-form-item>		
						</el-col>
						<el-col :span="6">
							<el-form-item label="机器ID">
								<el-input size="medium" v-model.trim="searchObj.machineId" maxlength="64"></el-input>
							</el-form-item>	
						</el-col>								
						<el-col :span="9">
							<el-form-item label="操作时间">
								<el-date-picker size="medium"
									v-model="searchObj.time"
									type="daterange"
									value-format="yyyy-MM-dd"
									range-separator="至"
									start-placeholder="开始日期"
									end-placeholder="结束日期" 
									unlink-panels>									
								</el-date-picker>
							</el-form-item>	
						</el-col>
						<el-col :span="3" class="text-right">
							<el-button class="search-btn" size="medium" @click="emptySearch">清空</el-button>
							<el-button class="search-btn" size="medium" type="primary" @click="searchHistoryList">搜索</el-button>
						</el-col>									
					</el-form>
				</el-row>
			</div>						
			<div class="sense-table-list sense-table-ellipsis">
				<el-table :data="historyListData" border>
					<el-table-column prop="machineId" label="机器ID" min-width="15%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="softwareName" label="软件名称" min-width="13%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="createUser" label="操作账号" min-width="12%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="createAt" label="操作时间" min-width="14%"></el-table-column>	
					<el-table-column :formatter="commandIdFormatter" prop="commandId" label="反制措施" min-width="12%"></el-table-column>		
					<el-table-column prop="title" label="概要" min-width="12%" show-overflow-tooltip></el-table-column>				
					<el-table-column :formatter="statusFormatter" label="到达状态" min-width="8%"></el-table-column>
					<el-table-column :formatter="execStatusFormatter" label="执行状态" min-width="8%"></el-table-column>
					<el-table-column label="操作" min-width="6%">
						<template slot-scope="scope">
							<span class="ssicon ss-info operate-icon" title="查看" @click="viewHistory(scope.row)"></span>
					        <span v-if="scope.row.status == 1" class="ssicon ss-revoke operate-icon" title="撤销" @click="revokeHistory(scope.row)"></span>
					    </template>
					</el-table-column>		
				</el-table>
			</div>
			<div class="sense-table-pager" v-if="total>0">
				<el-pagination
					background
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="currentPage"
					:page-sizes="[10,20,30]"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total">
				</el-pagination>
			</div>
		</div>
		<div class="all-dialog">
			<!-- 查看 start -->
			<el-dialog class="sense-dialog" title="反制历史记录查看" :visible.sync="viewFormVisible">
				<el-form :model="viewForm" size="small" label-width="100px">
					<el-form-item label="机器ID">
						<div class="sense-text">{{ viewForm.machineId }}</div>						
					</el-form-item>
					<el-form-item label="软件名称">
						<div class="sense-text">{{ viewForm.softwareName }}</div>						
					</el-form-item>
					<el-form-item label="操作账号">
						<div class="sense-text">{{ viewForm.createUser }}</div>
					</el-form-item>	
					<el-form-item label="操作时间">
						<div class="sense-text">{{ viewForm.createAt }}</div>
					</el-form-item>
					<el-form-item label="反制措施">
						<div class="sense-text">{{ viewForm.commandId |commandIdFilter }}</div>
					</el-form-item>	
					<el-form-item label="概要">
						<div class="sense-text">{{ viewForm.title }}</div>
					</el-form-item>
					<el-form-item label="措施描述">
						<div class="sense-text">{{ viewForm.desc }}</div>
					</el-form-item>
					<el-form-item label="到达状态">
						<div class="sense-text">{{ viewForm.status | statusFilter }}</div>
					</el-form-item>	
					<el-form-item label="到达时间">
						<div class="sense-text">{{ viewForm.downTime }}</div>
					</el-form-item>
					<el-form-item label="执行状态">
						<div class="sense-text">{{ viewForm.execStatus | execStatusFilter }}</div>
					</el-form-item>	
					<el-form-item label="执行时间">
						<div class="sense-text">{{ viewForm.finishTime }}</div>
					</el-form-item>	
					<el-form-item label="失败原因" v-if="viewForm.execStatus == 4">
						<div class="sense-text">{{ viewForm.failReason }}</div>
					</el-form-item>			
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button type="primary" size="small" @click="viewFormVisible = false">确 定</el-button>
				</div>
			</el-dialog>
			<!-- 查看 end -->
		</div>
	</div>
</template>

<script>

	export default {
		data() {
			return {
				isPreview: '',
				previewObj: {
					historyListData: [
						{'machineId': '19212122abzzsrty', 'softwareName': 'word.exe', 'guid': '1000001', 'createUser': '<EMAIL>', 'createAt': '2019-03-26 10:50:00', 'commandId': 1, 'title': '盗版软件提示', 'status': 4, 'execStatus': 1, 'desc': '措施描述'},
						{'machineId': '19212155abzzsrbb', 'softwareName': 'photoshop.exe', 'guid': '1000002', 'createUser': '<EMAIL>', 'createAt': '2019-03-26 00:20:00', 'commandId': 2, 'title': 'ahsDB_0800000000000161_1.1.1.9015.db', 'status': 1, 'execStatus': 1, 'desc': '措施描述'},
						{'machineId': '19212133abzzsraa', 'softwareName': 'excel.exe', 'guid': '1000003', 'createUser': '<EMAIL>', 'createAt': '2019-03-25 08:50:00', 'commandId': 1, 'title': '疑似盗版提示', 'status': 2, 'execStatus': 2, 'desc': '措施描述'},
					],
					total: 10
				},
				machineId: '',
				id: '',
				historyListData: [],  //反制历史列表数组
				currentPage: 1,  //当前页
				pageSize: 10,  //每页条数
				total: 0,  //数据总数
				searchObj: {
					machineId: '',
					cmdId: '',
					time: ''
				},
				viewFormVisible: false,
				viewForm: {},	
			}
		},
		filters: {
			//反制措施
			commandIdFilter: function(val) {
				switch(val) {
					case 1:
						return '消息通知';
						break;
					case 2:
						return '推送反黑数据库';
						break;
				};
			},
			//到达状态
			statusFilter: function(val) {
				switch(val) {
					case 1:
						return '未到达';
						break;
					case 2:
						return '已到达';
						break;
					case 4:
						return '已撤销';
						break;
				};
			},
			//执行状态
			execStatusFilter: function(val) {
				switch(val) {
					case 1:
						return '未执行';
						break;
					case 2:
						return '执行成功';
						break;
					case 4:
						return '执行失败';
						break;
				};
			},
		},
		methods: {
			getHistoryList() {	
				if (this.isPreview == 1) {
					this.historyListData = this.previewObj.historyListData;
					this.total = this.previewObj.total;
				} else {
				};					
			},
			//格式化反制措施
			commandIdFormatter(row, index) {
				switch(row.commandId) {
					case 1:
						return '消息通知';
						break;
					case 2:
						return '推送反黑数据库';
						break;
				};
			},	
			//格式化到达状态
			statusFormatter(row, index) {
				switch(row.status) {
					case 1:
						return '未到达';
						break;
					case 2:
						return '已到达';
						break;
					case 4:
						return '已撤销';
						break;
				};
			},
			//格式化执行状态	
			execStatusFormatter(row, index) {
				switch(row.execStatus) {
					case 1:
						return '未执行';
						break;
					case 2:
						return '执行成功';
						break;
					case 4:
						return '执行失败';
						break;
				};
			},		
			//切换每页显示条数
			handleSizeChange(val) {
				this.pageSize = val;
				this.getHistoryList();
			},
			//切换当前页
			handleCurrentChange(val) {
				this.currentPage = val;
				this.getHistoryList();
			},
			//搜索
			searchHistoryList() {
				this.currentPage = 1;
				this.getHistoryList();
			},
			//清空
			emptySearch() {
				this.searchObj.machineId = '';
				this.searchObj.cmdId = '';
				this.searchObj.time = '';
				this.currentPage = 1;
				this.getHistoryList();
			},
			//撤销
			revokeHistory(row) {
				this.$confirm('确认撤销吗？', '撤销', {
					type: 'warning'
				}).then(() => {
					if (this.isPreview == 1) {
						this.$message({
							message: '预览状态下无法完全模拟真实场景，请使用账号登录后操作',
							type: 'warning'
						});
					} else {
					};					
				}).catch(() => {

				});
			},
			//查看
			viewHistory(row) {
				this.viewForm = row;
				this.viewFormVisible = true;
			}
		},
		mounted() {
			this.isPreview = sessionStorage.getItem('isPreview');
			this.getHistoryList();
		},
	}
</script>

<style scoped lang="scss">	

</style>