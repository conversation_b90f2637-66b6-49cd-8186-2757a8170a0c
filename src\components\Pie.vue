<template>
    <div class="ahs-pie" id="ahs_pie"></div>
</template>

<script>

    export default {
        props: ['pieObj'], 
        data() {
            return {
                isPreview: '',
                previewObj: {
                    "dataList":[
                        {value:3350, name:'A软件'},
                        {value:3100, name:'B软件'},
                        {value:2340, name:'C软件'},
                        {value:1350, name:'D软件'},
                        {value:15480, name:'E软件'}
                    ],
                    "nameArr":['A软件','B软件','C软件','D软件','E软件']
                },
            }
        },      
        methods: { 
            //柱状图
            loadPie() {               
                let pie = this.$echarts.init(document.getElementById('ahs_pie'));

                if (this.isPreview == 1) {
                    let dataList = this.previewObj.dataList;
                    let nameArr = this.previewObj.nameArr;
                    
                    let option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: "{b}: {c} ({d}%)"
                        },                       
                        legend: {
                            orient: 'vertical',                                
                            left: 276,               
                            y: 'center',
                            right: 30,                            
                            itemWidth: 10,
                            itemHeight: 10,
                            data: nameArr,                            
                            tooltip: {
                                show: true
                            }                           
                        },
                        series: [
                            {
                                //name:'访问来源',
                                type:'pie',
                                radius: ['40%', '55%'],
                                center: ['40%', '50%'],
                                avoidLabelOverlap: false,
                                label: {
                                    normal: {
                                        show: false,
                                        position: 'center'
                                    },
                                },
                                labelLine: {
                                    normal: {
                                        show: false
                                    }
                                },
                                data: dataList
                            }
                        ]
                    };
                    pie.setOption(option);
                } else {
                }                               
            },         
        },       
        mounted(){
            this.isPreview = sessionStorage.getItem('isPreview');
            this.loadPie();
        }
    }
</script>

<style lang="scss">   
    .ahs-pie {
        width: 400px;
        height: 325px;
    }
</style>

