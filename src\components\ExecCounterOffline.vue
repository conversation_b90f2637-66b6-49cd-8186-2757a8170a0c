<template>
    <div>
        <div class="sense-main">
            <el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
            <el-link style="margin-left: 60px;" icon="el-icon-odometer" type="primary" @click="gocurdefinedhistory">当前APP反制记录</el-link>
            <el-row>
                <el-col :span="6">
                    <div class="grid-content bg-purple">
                        客户端:
                        <el-input placeholder="B8-WIN8X64" v-model="clifqdn" :disabled="true" class="wr">
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        在线状态:
                        <el-input placeholder="在线" v-model="clistatus" :disabled="true" class="wr">
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple">
                        软件名称:
                        <el-input placeholder="AppName" v-model="cliappname" :disabled="true" class="wr">
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        进程ID:
                        <el-input placeholder="102345" v-model="clipid" :disabled="true" class="wr">
                        </el-input>
                    </div>
                </el-col>
            </el-row>
            <br>

            打击策略组合
            <el-divider direction="horizontal"></el-divider>

            <el-row :gutter="12">
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked1" style="width: 45%;">策略
                            电子取证:追溯留存盗版侵权证据；使用盗版软件的机器留存证据</el-checkbox>
                        APPDATA取证目录 :
                        <el-input v-model="input1" placeholder="请输入内容" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked2" style="width: 45%;">策略 弹窗提醒</el-checkbox>
                        弹窗内容 :
                        <el-input v-model="input2" placeholder="请输入内容" style="width: 20%;"></el-input>
                        弹窗标题 :
                        <el-input v-model="input3" placeholder="请输入内容" style="width: 20%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked3" style="width: 45%;">策略 程序锁定；效果盗版程序二次启动时失效。</el-checkbox>
                        锁定程序 :
                        <el-input v-model="input4" placeholder="请输入内容" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked4" style="width: 45%;">策略 延迟退出进程 效果：隐蔽强制盗版程序即时退出。</el-checkbox>
                        延迟（秒） :
                        <el-input-number v-model="num" controls-position="right" @change="handleChange"
                            :min="0"></el-input-number>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked5" style="width: 45%;">策略 锁Windos屏 效果：让操作员无法继续操作</el-checkbox>
                        操作选项 :
                        <el-select v-model="value" clearable placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked6" style="width: 45%;">策略 关闭加密锁句柄； 效果：软件无法识别盗版锁。</el-checkbox>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked7" style="width: 45%;">策略 盗版锁自锁定-用户级；加密锁自锁，可解锁</el-checkbox>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked8" style="width: 45%;">策略 盗版锁自锁定-系统级；加密锁自锁，无法恢复</el-checkbox>
                    </el-card>
                </el-col>
                <el-col :span="24">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked9" style="width: 45%;">策略 锁定工程文件</el-checkbox>
                        文件后缀 :
                        <el-input v-model="input5" placeholder="请输入(例:exe)" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
            </el-row>
            <el-divider direction="horizontal"></el-divider>
            下发策略配置文件数据内容
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容" v-model="textarea2">
            </el-input>
            <p></p>
            <br>
            <el-row>
                <el-button type="primary" @click="checkandsend" style="margin-left: 90%;">上线后执行</el-button>
            </el-row>

        </div>
    </div>
</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            clifqdn: '',
            clistatus: '',
            cliappname: '',
            climechineid: '',
            developerindex: 0,
            appid :0,
            clipid: 0,

            input1: '',
            input2: '',
            input3: '',
            input4: '',
            input5: '',
            num: 0,
            checked1: false,
            checked2: false,
            checked3: false,
            checked4: false,
            checked5: false,
            checked6: false,
            checked7: false,
            checked8: false,
            checked9: false,
            textarea2: '',
            options: [{
                value: '1',
                label: 'Lockdownstation'
            }, {
                value: '2',
                label: 'ExitWindows'
            }
            ],
            value: ''
        }
    },
    mounted() {
        this.clifqdn = this.$store.state.ExecMsg.cliFQDN
        if (this.$store.state.ExecMsg.cliSTATUS == 1) {
            this.clistatus = '在线'
        } else {
            this.clistatus = '离线'
        }
        this.cliappname = this.$store.state.ExecMsg.appNAME
        this.climechineid = this.$store.state.ExecMsg.cliMechined
        this.clipid = this.$store.state.ExecMsg.appID
        this.developerindex = this.$store.state.ExecMsg.developerIndex
        this.appid = this.$store.state.ExecMsg.appId
    },
    methods: {
        goback() {
            this.$router.go(-1);
        },
        gocurdefinedhistory() {
            this.$router.push('/curdefinedhistory');
        },

        handleChange(item) {
            console.log(`${item} was changed`)
        },

        // 下发策略配置文件
        checkandsend() {
            if (this.checked1) {
                if (!this.isValidFilePath(this.input1)) {
                    this.$message({
                        message: '请输入正确的文件路径',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked2) {
                if (this.input3 == '' || this.input2 == '') {
                    this.$message({
                        message: '弹窗内容与标题不可为空',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked3) {
                if (this.input4 == '') {
                    this.$message({
                        message: '请输入程序名称',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked5) {
                if (this.value == 0) {
                    this.$message({
                        message: '请选择操作选项',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked9) {
                if (this.input5 == '') {
                    this.$message({
                        message: '请输入文件后缀',
                        type: 'warning'
                    });
                    return
                }
            }


            let url = '/senddefined';
            let param = {
                "ch1": this.checked1,
                "ch2": this.checked2,
                "ch3": this.checked3,
                "ch4": this.checked4,
                "ch5": this.checked5,
                "ch6": this.checked6,
                "ch7": this.checked7,
                "ch8": this.checked8,
                "ch9": this.checked9,
                "developerindex": this.developerindex,
                "appid": this.appid,
                "clifqdns": this.clifqdn,
                "cliappname": this.cliappname,
                "climechineid": this.climechineid,
                "windowscheck": this.value,
                "IFEO": this.input1,
                "MessageText": this.input2,
                "MessageTitle": this.input3,
                "IFEO": this.input4,
                "Lockfile": this.input5,
                "MessageDelayTime": this.num,

            };
            apiClient.post(url, param).then(res => {
                if (res.data.code == 0) {
                    this.$message({
                        message: "success 0x0000000",
                        type: 'success'
                    });
                }else{
                    this.$message({
                        message: res.data.msg,
                        type: 'warning'
                    });
                }
            })
        },
        // 验证文件路径
        isValidFilePath(filePath) {
            // Regular expressions for Windows and Unix file paths
            const windowsPathRegex = /^[a-zA-Z]:((\\)[\S].+\s?)*\\$/;
            const unixPathRegex = /^\/([\u4E00-\u9FA5A-Za-z0-9_]+\/{1})+$/;

            // Check if the file path matches either the Windows or Unix regex
            return windowsPathRegex.test(filePath) || unixPathRegex.test(filePath);
        }
    }
}
</script>

<style scoped lang="scss">
.wr {
    width: 50%;
}


.el-col {
    padding: 5px;
    //margin-bottom: 1px;
}
</style>