<template>
    <div>
        <div class="sense-main">
            <div class="sense-table-list sense-table-ellipsis">
                <template>
                    <el-table border style="width: 100%" ref="multipleTable" @selection-change="handleSelectionChange"
                        :cell-style="{ padding: '7px' }" align="center" :data="exceptionListData">
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <el-table-column align="center" label="盗版类型" min-width="8%">
                            <template slot-scope="scope">
                                <span v-if="scope.row.viewdata.piracy == 0"
                                    class="sense-mestype sense-mestype-0">纯正版</span>
                                <span v-if="scope.row.viewdata.piracy == 1"
                                    class="sense-mestype sense-mestype-2">纯盗版</span>
                                <span v-if="scope.row.viewdata.piracy == 2"
                                    class="sense-mestype sense-mestype-1">正盗混用</span>
                                <span v-if="scope.row.viewdata.piracy == 3"
                                    class="sense-mestype sense-mestype-5">锁信息篡改</span>
                                <span v-if="scope.row.viewdata.piracy == 4"
                                    class="sense-mestype sense-mestype-2">可疑</span>
                                <span v-if="scope.row.viewdata.piracy == 5"
                                    class="sense-mestype sense-mestype-2">需排查</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="viewdata.fqdn" label="终端名称FQDN" min-width="8%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.guid" label="机器ID(Macine-GUID)" min-width="10%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="finallocation" label="省/市" min-width="5%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.softinfo" label="软件信息" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="locktyperes" label="锁类型" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" label="离线锁验真" min-width="8%" show-overflow-tooltip>res
                            <template slot-scope="scope">
                                <span v-if="scope.row.viewdata.lockIsPiracy == 0"
                                    class="sense-mestype sense-mestype-0">未上传</span>
                                <span v-if="scope.row.viewdata.lockIsPiracy == 1"
                                    class="sense-mestype sense-mestype-5">正版锁</span>
                                <span v-if="scope.row.viewdata.lockIsPiracy == 2"
                                    class="sense-mestype sense-mestype-2">盗版锁</span>
                                <el-link v-if="scope.row.viewdata.lockIsPiracy == 3" type="primary">存在多个结果</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="viewdata.pchipsn" label="盗版锁芯片号" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.pcasesn" label="盗版锁外壳SN" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.lcasesn" label="正版外壳锁号" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.lchipsn" label="正版锁芯片号" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" label="反黑检测结果" min-width="6%" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-link v-if="scope.row.viewdata.ismanyAHSres == 1" type="primary">存在多个结果</el-link>
                                <span v-if="scope.row.viewdata.AHSres == 0" class="sense-mestype">未发现盗版补丁</span>
                                <span v-else class="sense-mestype">盗版补丁 {{ scope.row.viewdata.AHSres }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="viewdata.lastUpdateTime" label="数据更新时间" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" prop="viewdata.version" label="库版本" min-width="6%"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" label="数据判断" min-width="8%" show-overflow-tooltip>
                            <el-button size="mini">服务器校验</el-button>
                        </el-table-column>
                        <el-table-column align="center" label="客户端操作" min-width="7%" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <router-link :to="{ path: '/execCounter' }">
                                    <el-button size="mini" @click="handleClick(scope.row)">盗版打击</el-button>
                                </router-link>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="sense-table-pager" v-if="total > 0">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 20, 30]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
        <div class="all-dialog">

        </div>
    </div>
</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            dialogFormVisible: false,
            isPreview: '',
            previewObj: {
                exceptionListData: [
                    {
                        'appid': 1, 'fqdn': 'liujx-epc.sense.com.cn', 'guid': '4eee6cfcbd82ab47b8a1d4e65a8b4d54',
                        'AHSres': 1, 'isPiracyLic': 0, 'ismanyAHSres': 0, 'ismanyLockPiracy': 0, 'ismanyPiracyId': 0,
                        'lastUpdateTime': '2024-09-09 18:02:02', 'lcasesn': '3375000003', 'lchipsn': '3375000003', 'location': '北京-北京',
                        'lockIsPiracy': 1, 'lockType': '精锐4', 'pcasesn': '3375000003', 'pchipsn': '3375000003', 'piracy': 0, 'softinfo': 'debug.exe'
                        , 'version': '*******', 'finalres': '纯盗版'
                    },
                    {
                        'appid': 2, 'fqdn': 'liujx-epc.sense.com.cn', 'guid': '4eee6cfcbd82ab47b8a1d4e65a8b4d54',
                        'AHSres': 14, 'isPiracyLic': 0, 'ismanyAHSres': 0, 'ismanyLockPiracy': 0, 'ismanyPiracyId': 0,
                        'lastUpdateTime': '2024-09-09 18:02:02', 'lcasesn': '3375000003', 'lchipsn': '3375000003', 'location': '北京-北京',
                        'lockIsPiracy': 2, 'lockType': '精锐5', 'pcasesn': '3375000003', 'pchipsn': '3375000003', 'piracy': 0, 'softinfo': 'debug.exe'
                        , 'version': '*******', 'finalres': '纯正版'
                    },
                    {
                        'appid': 3, 'fqdn': 'liujx-epc.sense.com.cn', 'guid': '4eee6cfcbd82ab47b8a1d4e65a8b4d54',
                        'AHSres': 30, 'isPiracyLic': 0, 'ismanyAHSres': 0, 'ismanyLockPiracy': 0, 'ismanyPiracyId': 0,
                        'lastUpdateTime': '2024-09-09 18:02:02', 'lcasesn': '3375000003', 'lchipsn': '3375000003', 'location': '北京-北京',
                        'lockIsPiracy': 0, 'lockType': '精锐4', 'pcasesn': '3375000003', 'pchipsn': '3375000003', 'piracy': 0, 'softinfo': 'debug.exe'
                        , 'version': '*******', 'finalres': '正盗混用'
                    },
                    {
                        'appid': 4, 'fqdn': 'liujx-epc.sense.com.cn', 'guid': '4eee6cfcbd82ab47b8a1d4e65a8b4d54',
                        'AHSres': 0, 'isPiracyLic': 0, 'ismanyAHSres': 0, 'ismanyLockPiracy': 0, 'ismanyPiracyId': 0,
                        'lastUpdateTime': '2024-09-09 18:02:02', 'lcasesn': '3375000003', 'lchipsn': '3375000003', 'location': '北京-北京',
                        'lockIsPiracy': 3, 'lockType': '精锐5+精锐4', 'pcasesn': '3375000003', 'pchipsn': '3375000003', 'piracy': 0, 'softinfo': 'debug.exe'
                        , 'version': '*******', 'finalres': '可疑'
                    },
                ],
                total: 21
            },
            exceptionListData: [],  // 展示数据
            currentPage: 1,  //当前页
            pageSize: 10,  //每页条数
            total: 0,  //数据总数	
            Dialogdata: {}, // 存储app详细信息
            multipleSelection: [] // 多选数据
        }
    },
    // computed: {
    // 	currentPageData() {
    // 		const start = (this.currentPage - 1) * this.pageSize;
    // 		const end = start + this.pageSize;
    // 		return this.exceptionListData.slice(start, end);
    // 	}
    // },
    methods: {
        handleClick(row) {
            this.$store.commit('SET_EXEC_cliFQDN', row.viewdata.fqdn);
            this.$store.commit('SET_EXEC_appNAME', row.viewdata.softinfo);
            this.$store.commit('SET_EXEC_appId', row.viewdata.appid);
            this.$store.commit('SET_EXEC_cliMechined', row.viewdata.guid);
            this.$store.commit('SET_EXEC_developerIndex', 1);
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        getExceptionList() {
            // 预览
            if (this.isPreview == 1) {
                this.exceptionListData = this.previewObj.exceptionListData;
                this.total = this.previewObj.total;
            } else {
                // 非预览	
            };
        },
        //格式化省市
        areaFormatter(row, index) {
            return row.province + '-' + row.city;
        },
        //切换每页显示条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getadddata();
        },
        //切换当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.$store.commit('SET_CUR_PAGE', val);
            this.getadddata();
        },
        getadddata() {
            let url = '/getviewdata';
            apiClient.post(url).catch(error => {
                this.$message.error("server:" + error.message);
                return
            })
                .then(res => {
                    if (res.data.code == 0) {
                        // for (let i = 0; i < res.data.results.length; i++) {
                        //     // 数据转码
                        //     res.data.results[i].addinfo.internet_ipv4 = atob(res.data.results[i].addinfo.internet_ipv4)
                        //     res.data.results[i].addinfo.machine_id = this.base64ToHex(res.data.results[i].addinfo.machine_id)
                        //     // res.data.results[i].addinfo.host_mac = atob(res.data.results[i].addinfo.host_mac)
                        // }
                        // // 获取数据
                        // this.exceptionListData = res.data.results;
                        // // 更新总条数
                        // this.total = res.data.results.length;
                        this.exceptionListData = res.data.res;
                        this.total = res.data.res.length;
                    } else {
                        this.$message({
                            message: "error:" + res.data.msg,
                            type: 'warning'
                        });
                    }
                }
                )
        },
        dialogClick(row) {
            this.Dialogdata = row.addinfo.app_pe_info;
            this.dialogFormVisible = true;
        }
    },
    mounted() {
        this.getadddata();
    },
}
</script>

<style scoped lang="scss"></style>