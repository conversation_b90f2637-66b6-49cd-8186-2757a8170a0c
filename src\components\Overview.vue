<template>
	<div>
		<div class="sense-card">
			<el-row>
				<el-col :span="6">
					<div class="sense-card-main">
						<p class="sense-card-title">总装机量(APP-GUID)</p>
						<h2 class="sense-card-num">{{ dailyData.count1 }}</h2>
						<el-tooltip class="item" effect="dark" content="总的APP-GUID映射数量" placement="bottom">
							<span class="ssicon ss-info sense-card-info"></span>
						</el-tooltip>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="sense-card-main">
						<p class="sense-card-title">盗版软件</p>
						<h2 class="sense-card-num">{{ dailyData.count2 }}</h2>
						<el-tooltip class="item" effect="dark" placement="bottom">
							<div class="sense-slot" slot="content">
								检测到的盗版软件的总数量<br>
								<!-- 在检测过程中，存在以下等现象：<br>
								1. 发现盗版模块名称特征;<br>
								2. 发现盗版模块内存特征;<br>
								3. 发现盗版设备的特征（如盗版锁）。 -->
							</div>
							<span class="ssicon ss-info sense-card-info"></span>
						</el-tooltip>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="sense-card-main">
						<p class="sense-card-title">未审核数量</p>
						<h2 class="sense-card-num">{{ dailyData.count3 }}</h2>
						<el-tooltip class="item" effect="dark" placement="bottom">
							<div class="sense-slot" slot="content">
								尚未进行复核存盘的数量<br>
							</div>
							<span class="ssicon ss-info sense-card-info"></span>
						</el-tooltip>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="sense-card-main">
						<p class="sense-card-title">软件启动总数</p>
						<h2 class="sense-card-num">{{ dailyData.count4 }}</h2>
						<el-tooltip class="item" effect="dark" placement="bottom">
							<div class="sense-slot" slot="content">
								软件每启动一次就会收集一次数量<br>
							</div>
							<span class="ssicon ss-info sense-card-info"></span>
						</el-tooltip>
					</div>
				</el-col>
			</el-row>
		</div>
		<div class="sense-chart">
			<div class="sense-trend">
				<div class="sense-trend-title clearfix">
					<a v-for="item in tabData" :class="{ active: tabNameActive == item.key }"
						@click="softTypeClick(item.key)" href="javascript:;" class="fl">{{ item.label }}</a>
				</div>
				<div class="sense-trend-con clearfix">
					<div class="fl posi-relative">
						<ahs-line :lineObj="searchObj" ref="lineChart"></ahs-line>
						<h3 class="sense-chart-title">{{ tabData[tabNameActive - 1].title }}</h3>
					</div>
					<div class="fl" style="width: 50%;">
						<el-col v-if="tabNameActive == '2'" :span="10">
							<el-card style="height: 10rem;" shadow="always">
								<p class="sense-card-title">最近3个月在线软件总数</p>
								<h2 class="sense-card-num" style="font-size: 28px;padding-top: 10px;">{{ online3monthcount }}</h2>
							</el-card>
						</el-col>
						<el-col v-if="tabNameActive == '2'" style="margin-left: 5%" :span="10">
							<el-card style="height: 10rem;" shadow="always">
								<p class="sense-card-title">最近1年在线软件总数</p>
								<h2 class="sense-card-num" style="font-size: 28px;padding-top: 10px;">{{ online1yearcount }}</h2>
							</el-card>
						</el-col>
						<!-- <ahs-pie :pieObj="searchObj" ref="pieChart"></ahs-pie> -->
					</div>
				</div>
			</div>

			<div class="sense-extra">
				<a v-for="item in trendList" v-if="tabNameActive == '1'" :class="{ active: trendActive == item.value }"
					@click="trendClick(item.value)" href="javascript:;">{{ item.name }}</a>
			</div>
		</div>
		<div class="sense-map">
			<h2 class="sense-map-title">原始记录地区分布</h2>
			<div class="clearfix">
				<div class="fl sense-china-map">
					<world-map :mapObj="mapSearchObj" class="map-conp" ref="mapChart"></world-map>
				</div>
				<div class="fr sense-top10">
					<h3>Top10地区</h3>
					<ul class="sense-ranking-list" v-if="top10Arr && top10Arr.length > 0">
						<li class="clearfix" v-for="(item, index) in top10Arr" :class="{ active: index < 5 }">
							<span class="fl sense-ranking-list-num">{{ index + 1 }}</span>
							<span class="fl sense-ranking-list-name">{{ item.city }}</span>
							<span class="fl sense-ranking-list-val">{{ item.online }}</span>
						</li>
					</ul>
					<div class="no-data-tip" v-else style="padding: 20px; text-align: center; color: #909399;">
						暂无数据
					</div>
				</div>
				<div class="fr sense-top10">
					<h3>盗版软件公司分布</h3>
					<ul class="sense-ranking-list" v-if="top10PirateArr && top10PirateArr.length > 0">
						<li class="clearfix" v-for="(item, index) in top10PirateArr" :class="{ active: index < 5 }">
							<span class="fl sense-ranking-list-num">{{ index + 1 }}</span>
							<span class="fl sense-ranking-list-name">{{ item.company_name }}</span>
							<span class="fl sense-ranking-list-val">{{ item.count }}</span>
						</li>
					</ul>
					<div class="no-data-tip" v-else style="padding: 20px; text-align: center; color: #909399;">
						不存在盗版
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import AhsLine from './Line.vue'
import WorldMap from './ChinaMap.vue'
import AhsPie from './Pie.vue'
import apiClient from '../api/axios'

export default {
	data() {
		return {
			isPreview: true,
			previewObj: {
				dailyData: {  //每日统计
					count1: 126560,  //在线软件
					count2: 6560,  //可疑行为
					count3: 560,  //疑似盗版
					count4: 60  //盗版软件
				},
				top10Arr: [
					{ "count": 9990, "province": "黑龙江", "city": "大庆" },
					{ "count": 4570, "province": "内蒙古", "city": "包头" },
					{ "count": 1200, "province": "浙江", "city": "温州" },
					{ "count": 700, "province": "江西", "city": "九江" },
					{ "count": 333, "province": "四川", "city": "成都" },
					{ "count": 301, "province": "湖南", "city": "长沙" },
					{ "count": 199, "province": "陕西", "city": "西安" },
					{ "count": 160, "province": "湖北", "city": "武汉" },
					{ "count": 100, "province": "河南", "city": "郑州" },
					{ "count": 99, "province": "河北", "city": "石家庄" }
				],
			},
			dailyData: {  //每日统计
				count1: 0,  //在线软件
				count2: 0,  //可疑行为
				count3: 0,  //疑似盗版
				count4: 0  //盗版软件
			},
			tabNameActive: '1',
			tabData: [
				{ key: '1', label: '总装机量', title: '总装机量趋势' },
				{ key: '2', label: '在线软件', title: '在线软件趋势' },
				{ key: '3', label: '盗版软件', title: '盗版软件趋势' }
			],
			searchObj: {
				type: 1,
				timeType: 1,
			},
			trendList: [
				{ name: '最近30天', value: '1' },
				{ name: '最近3个月', value: '2' },
				{ name: '最近1年', value: '3' },
			],
			trendActive: '1',
			mapSearchObj: {
				type: 1,
				timeType: 0,
			},
			mapActive: '0',
			top10Arr: [],
			top10PirateArr: [],
			areaList: [
				{ "name": "包头", "value": 4570 },
				{ "name": "温州", "value": 1200 },
				{ "name": "九江", "value": 700 },
				{ "name": "成都", "value": 333 },
				{ "name": "长沙", "value": 301 },
				{ "name": "西安", "value": 199 },
				{ "name": "武汉", "value": 160 },
				{ "name": "郑州", "value": 100 },
			],
			linedate: [],
			linevalue: [],
			online3monthcount: '',
			online1yearcount: '',
		}
	},
	components: {
		'ahs-line': AhsLine,
		'ahs-pie': AhsPie,
		'world-map': WorldMap,
	},
	mounted() {
		this.isPreview = true;
		// this.dailyData = this.previewObj.dailyData;
		this.top10Arr = this.previewObj.top10Arr;
		this.getMapData();
		this.getLineData30();
		this.getTotalViewCount();
		this.getTotalPirateCount();
		this.getUncheckCount();
		this.getTotalStartCount();
		this.getTop10Pirate();
	},
	methods: {
		mapTypeChange() {
			this.$refs.mapChart.updateMap([{ "name": "石家庄", "value": 99 },
			{ "name": "上海", "value": 25 },
			{ "name": "攀枝花", "value": 25 },
			{ "name": "三亚", "value": 28 },
			{ "name": "福州", "value": 29 },
			{ "name": "瓦房店", "value": 30 },
			{ "name": "即墨", "value": 30 },])
		},

		// 获取盗版软件公司分布
		getTop10Pirate() {
			apiClient.get('/gettoptenpiracycompanies').then(res => {
				if (res.data.code == 0) {
					this.top10PirateArr = res.data.data;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取地图数据
		getMapData() {
			apiClient.get('/getonlinespace').then(res => {
				if (res.data.code == 0) {
					this.areaList = [];  // 清空原有数据
					if (res.data.data != null) {
						res.data.data.forEach(item => {
							this.areaList.push({
								"name": item.city,
								"value": item.online
							})
						})
						// top10地区
						this.top10Arr = res.data.data.slice(0, 10);
					}
					this.$refs.mapChart.getMap(this.areaList)
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 折线图切换-标题
		softTypeClick(val) {
			if (val == '1') {
				this.tabNameActive = '1';
				this.trendClick('1');
			} else if (val == '2') {
				this.tabNameActive = '2';
				this.trendClick('2');
			} else if (val == '3') {
				this.tabNameActive = '3';
				this.getLinePirate30();
			}
		},

		// 折线图切换-数据量
		trendClick(val) {
			if (this.tabNameActive == '1') {
				if (val == '1') {
					this.trendActive = '1';
					this.getLineData30();
				} else if (val == '2') {
					this.trendActive = '2';
					this.getLineData3month();
				} else if (val == '3') {
					this.trendActive = '3';
					this.getLineData1year();
				}
			}
			if (this.tabNameActive == '2') {
				this.getLineData30Online();
				this.getLineData3monthOnline();
				this.getLineData1yearOnline();
			}
			if (this.tabNameActive == '3') {
				this.getLinePirate30();
			}
		},

		// 获取总装机量折线图30天数据
		getLineData30() {
			apiClient.get('/getappguidcount30days').then(res => {
				if (res.data.code == 0) {
					this.linedate = [];  // 清空原有数据
					this.linevalue = [];  // 清空原有数据
					if (res.data.date != null && res.data.count != null) {
						this.linedate = res.data.date;
						this.linevalue = res.data.count;
						this.$refs.lineChart.loadLine(this.linedate, this.linevalue)
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取总装机量折线图3个月数据
		getLineData3month() {
			apiClient.get('/getappguidcount3months').then(res => {
				if (res.data.code == 0) {
					this.linedate = [];  // 清空原有数据
					this.linevalue = [];  // 清空原有数据
					if (res.data.date != null && res.data.count != null) {
						this.linedate = res.data.date;
						this.linevalue = res.data.count;
						this.$refs.lineChart.loadLine(this.linedate, this.linevalue)
					}
				} else {
					this.$message.error(res.data.msg);
				}

			})
		},

		// 获取总装机量折线图1年数据
		getLineData1year() {
			apiClient.get('/getappguidcountyear').then(res => {
				if (res.data.code == 0) {
					this.linedate = [];  // 清空原有数据
					this.linevalue = [];  // 清空原有数据
					if (res.data.date != null && res.data.count != null) {
						this.linedate = res.data.date;
						this.linevalue = res.data.count;
						this.$refs.lineChart.loadLine(this.linedate, this.linevalue)
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取在线软件折线图30天数据
		getLineData30Online() {
			apiClient.get('/getonlinecount30days').then(res => {
				if (res.data.code == 0) {
					this.linedate = [];  // 清空原有数据
					this.linevalue = [];  // 清空原有数据
					if (res.data.date != null && res.data.count != null) {
						this.linedate = res.data.date;
						this.linevalue = res.data.count;
						this.$refs.lineChart.loadLine(this.linedate, this.linevalue)
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取最近3个月在线软件总数
		getLineData3monthOnline() {
			apiClient.get('/getonlinecount3months').then(res => {
				if (res.data.code == 0) {
					this.online3monthcount = res.data.count;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取最近1年在线软件总数
		getLineData1yearOnline() {
			apiClient.get('/getonlinecountyear').then(res => {
				if (res.data.code == 0) {
					this.online1yearcount = res.data.count;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取总装机量
		getTotalViewCount() {
			apiClient.get('/gettotalviewdatacount').then(res => {
				if (res.data.code == 0) {
					this.dailyData.count1 = res.data.data;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取盗版软件总数
		getTotalPirateCount() {
			apiClient.get('/getpiracyinfo').then(res => {
				if (res.data.code == 0) {
					this.dailyData.count2 = res.data.data;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取未审核数量
		getUncheckCount() {
			apiClient.get('/getuncheckedinfocount').then(res => {
				if (res.data.code == 0) {
					this.dailyData.count3 = res.data.data;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取软件启动总数
		getTotalStartCount() {
			apiClient.get('/getsoftwarestartcount').then(res => {
				if (res.data.code == 0) {
					this.dailyData.count4 = res.data.data;
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

		// 获取盗版软件折线图30天数据
		getLinePirate30() {
			apiClient.get('/getpiracycount30days').then(res => {
				if (res.data.code == 0) {
					this.linedate = [];  // 清空原有数据
					this.linevalue = [];  // 清空原有数据
					if (res.data.date != null && res.data.count != null) {
						this.linedate = res.data.date;
						this.linevalue = res.data.count;
						this.$refs.lineChart.loadLine(this.linedate, this.linevalue)
					}
				} else {
					this.$message.error(res.data.msg);
				}
			})
		},

	}
}

</script>

<style scoped lang="scss">
$common-color: #409EFF;
</style>