<template>
    <div class="world-map" id="world-map"></div>
</template>

<script>
    // 世界地图
    import '../../node_modules/echarts/map/js/world'
    export default {
      name: 'WorldMap',
      props: ['mapObj'], 
      data(){
        return{
          chinaMap: null,
        }
      },
      methods: { 
        getMap(data) {
          this.chinaMap = this.$echarts.init(document.getElementById('world-map'))
          let showLoadingDefault = {
            text: '加载中...',
            color: '#23531',
            textColor: '#999',
            // 地图背景色
            maskColor: '#fff',
            zlevel: 0
          }
          this.chinaMap.setOption({
            backgroundColor: '#fff',          
            // 地图上圆点的提示
            tooltip: {
              trigger: 'item',
              formatter: function (params) {
                return params.name + ' : ' + params.value[2]
              }
            },
            // 图例按钮 点击可选择哪些不显示
            legend: {
              orient: 'vertical',
              left: 'left',
              top: 'bottom',
              data: ['地区热度', 'top5'],
              textStyle: {
                color: '#666'
              }
            },
            // 地理坐标系组件
            geo: {
              map: 'world',
              label: {
                // true会显示城市名
                emphasis: {
                  show: true
                }
              },
              itemStyle: {
                // 地图背景色
                normal: {
                  areaColor: '#b6dcff',
                  borderColor: '#517aad'
                },
                // 悬浮时
                emphasis: {
                  areaColor: '#8796B4'
                }
              },
              // 显示全球区域
              zoom: 1,
              center: [0, 0], // 中心点设置为全球中心
              roam: true,  // 允许缩放
              scaleLimit: {min:1, max:20}
            },
            // 系列列表
            series: [
              {
                name: '地区热度',
                // 表的类型 这里是散点
                type: 'scatter',
                // 使用地理坐标系，通过 geoIndex 指定相应的地理坐标系组件
                coordinateSystem: 'geo',
                data: [],
                // 标记的大小
                symbolSize: 10,
                // 鼠标悬浮的时候在圆点上显示数值
                label: {
                  normal: {
                    show: false
                  },
                  emphasis: {
                    show: false
                  }
                },
                itemStyle: {
                  normal: {
                    color: '#f4ad25'
                  },
                  // 鼠标悬浮的时候圆点样式变化
                  emphasis: {
                    borderColor: '#fff',
                    borderWidth: 1
                  }
                }
              },
              {
                name: 'top5',
                // 表的类型 这里是散点
                type: 'effectScatter',
                // 使用地理坐标系，通过 geoIndex 指定相应的地理坐标系组件
                coordinateSystem: 'geo',
                data: [],
                // 标记的大小
                symbolSize: 20,
                showEffectOn: 'render',
                rippleEffect: {
                  brushType: 'stroke'
                },
                hoverAnimation: true,
                label: {
                  normal: {
                    show: false
                  }
                },
                itemStyle: {
                  normal: {
                    color: '#f47d25',
                    shadowBlur: 10,
                    shadowColor: '#f47d25'
                  }
                },
                zlevel: 1
              }
            ]
          })
          let chartObj = {
            chinaMap: this.chinaMap,
            searchObj: this.mapObj,
            data: data
          };
          this.chinaMap.showLoading(showLoadingDefault);
          this.$store.commit('openLoading');
          this.$store.dispatch('fetchHeatChinaRealData', chartObj)
        },
        updateMap(data){
          let chartObj = {
            chinaMap: this.chinaMap,
            searchObj: this.mapObj,
            data: data
          };
          this.$store.dispatch('fetchHeatChinaRealData', chartObj)
        }
      },
      mounted () {
        // this.getMap();
      },
    }
</script>

<style>
    .world-map{
        width: 850px;
        height: 850px;
    }
</style>
