<template>
    <div>
        <div class="sense-main">
            <el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
            <br>
            <br>
            <el-descriptions :column="4" border>
                <el-descriptions-item label="终端hostname">
                    {{ hostname }}
                </el-descriptions-item>
                <el-descriptions-item label="所属公司">
                    {{ companyname }}
                </el-descriptions-item>
                <el-descriptions-item label="软件名称">
                    {{ appname }}
                </el-descriptions-item>
                <el-descriptions-item label="机器唯一ID">
                    {{ guid }}
                </el-descriptions-item>
            </el-descriptions>

            <br>
            <el-row>
                <el-col :offset="3" :span="18">
                    <span style="font-size: large;"> 打击策略组合</span>
                    <span style="font-size: large; margin-left: 20%;"> 选择打击进程id : </span>
                    <el-select v-model="selectpid" clearable style="width: 250px;" placeholder="请选择打击进程号,默认离线打击">
                        <el-option v-for="item in pids" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <el-divider direction="horizontal"></el-divider>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked1" style="width: 45%;">策略
                            电子取证:追溯留存盗版侵权证据；使用盗版软件的机器留存证据</el-checkbox>
                        APPDATA取证目录 :
                        <el-input v-model="input1" placeholder="请输入内容" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked2" style="width: 45%;">策略 弹窗提醒</el-checkbox>
                        弹窗内容 :
                        <el-input v-model="input2" placeholder="请输入内容" style="width: 20%;"></el-input>
                        弹窗标题 :
                        <el-input v-model="input3" placeholder="请输入内容" style="width: 20%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked3" style="width: 45%;">策略 程序锁定；效果盗版程序二次启动时失效。</el-checkbox>
                        锁定程序 :
                        <el-input v-model="input4" placeholder="请输入内容" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked4" style="width: 45%;">策略 延迟退出进程 效果：隐蔽强制盗版程序即时退出。</el-checkbox>
                        延迟（秒） :
                        <el-input-number v-model="num" controls-position="right" @change="handleChange"
                            :min="0"></el-input-number>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked5" style="width: 45%;">策略 锁Windos屏 效果：让操作员无法继续操作</el-checkbox>
                        操作选项 :
                        <el-select v-model="value" clearable placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked7" style="width: 45%;height: 2rem;">策略
                            精锐4-盗版锁自锁定-用户级；加密锁自锁，可解锁</el-checkbox>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked8" style="width: 45%;height: 2rem;">策略
                            精锐4-盗版锁自锁定-系统级；加密锁自锁，无法恢复</el-checkbox>
                    </el-card>
                </el-col>
                <el-col :offset="3" :span="18">
                    <el-card shadow="hover" :body-style="{ padding: '5px' }">
                        <el-checkbox v-model="checked9" style="width: 45%;">策略 锁定数据文件</el-checkbox>
                        文件后缀 :
                        <el-input v-model="input5" placeholder="请输入(例:exe)" style="width: 30%;"></el-input>
                    </el-card>
                </el-col>
            </el-row>
            <el-row>
                <el-col :offset="3" :span="18">
                    <el-divider direction="horizontal"></el-divider>
                    <h1 style="font-size: large;">下发策略配置文件数据内容</h1>
                    <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容"
                        v-model="textarea2">
                    </el-input>
                </el-col>
            </el-row>

            <p></p>
            <br>
            <el-row>
                <el-button type="primary" @click="checkofflinedefeat" style="margin-left: 90%;">立即执行</el-button>
            </el-row>

        </div>
    </div>
</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            viewdataid: '',
            hostname: '',
            appid: '',
            companyname: '',
            appname: '',
            guid: '',
            pids: [],
            selectpid: '',

            input1: '',
            input2: '',
            input3: '',
            input4: '',
            input5: '',
            num: 0,
            checked1: false,
            checked2: false,
            checked3: false,
            checked4: false,
            checked5: false,
            checked7: false,
            checked8: false,
            checked9: false,
            textarea2: '',
            options: [{
                value: '1',
                label: 'Lockdownstation (win + L)'
            }, {
                value: '2',
                label: 'LogoutWindows (退出当前登录会话)'
            }],
            value: ''
        }
    },
    methods: {
        goback() {
            this.$router.go(-1);
        },
        gocurdefinedhistory() {
            this.$router.push('/curdefinedhistory');
        },

        handleChange(item) {
            console.log(`${item} was changed`)
        },

        // 检测离线打击
        checkofflinedefeat() {
            if (this.selectpid == '') {
                let url = '/lic/checkofflinedefeat';
                let param = {
                    "viewdataid": this.viewdataid,
                }
                apiClient.post(url, param).then(res => {
                    if (res.data.code == 0) {
                        if (res.data.count > 0) {
                            this.$confirm('已存在未执行的离线打击,继续发布将覆盖之前的离线打击,是否发布?', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                this.checkandsend();
                            }).catch(() => {
                                this.$message({
                                    type: 'info',
                                    message: '已取消'
                                });
                            });
                        } else {
                            this.checkandsend();
                        }
                    } else {
                        this.$message({
                            message: res.data.msg,
                            type: 'warning'
                        });
                    }
                })
            } else {
                this.checkandsend();
            }
        },

        // 下发策略配置文件
        checkandsend() {
            if (this.checked1) {
                if (!this.isValidFilePath(this.input1)) {
                    this.$message({
                        message: '请输入正确的文件路径',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked2) {
                if (this.input3 == '' || this.input2 == '') {
                    this.$message({
                        message: '弹窗内容与标题不可为空',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked3) {
                if (this.input4 == '') {
                    this.$message({
                        message: '请输入程序名称',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked5) {
                if (this.value == 0) {
                    this.$message({
                        message: '请选择锁屏操作选项',
                        type: 'warning'
                    });
                    return
                }
            }
            if (this.checked9) {
                if (this.input5 == '') {
                    this.$message({
                        message: '请输入文件后缀',
                        type: 'warning'
                    });
                    return
                }
            }


            let url = '/lic/senddefeat';
            let param = {
                "isforensics": this.checked1, // 电子取证
                "ismsgbox": this.checked2,  // 弹窗提醒
                "islockdownifeo": this.checked3, // 程序锁定
                "isdelayexit": this.checked4, // 延迟退出进程
                "islockworkstation": this.value == '1' ? true : false, // 锁屏 win+L
                "islogoutwinlogon": this.value == '2' ? true : false, // 注销 退出当前登录会话
                "islockdowns4type1": this.checked7, // 精锐4-盗版锁自锁定-用户级
                "islockdowns4type2": this.checked8,
                "islockdownfile": this.checked9, // 锁定数据文件

                "viewdataidstr": this.viewdataid,
                "companyname": this.companyname,
                "appid": this.appid,

                "companyname": this.input1,
                "messagetext": this.input2,
                "messagetitle": this.input3,
                "ifeo": this.input4,
                "lockfile": this.input5,
                "exitdelaytime": this.num,
            };
            if (this.selectpid == '') {
                param["selectpid"] = 0;
            } else {
                param["selectpid"] = Number(this.selectpid);
            }
            apiClient.post(url, param).then(res => {
                if (res.data.code == 0) {
                    this.$message({
                        message: "发送成功",
                        type: 'success'
                    });
                } else {
                    this.$message({
                        message: res.data.msg,
                        type: 'warning'
                    });
                }
            })
        },
        getPids() {
            let param = {
                "viewdataid": this.viewdataid,
            }
            apiClient.post('/getonlinepids', param).catch(error => {
                this.$message.error("server:" + error.message);
                return
            })
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.res != null) {
                            this.pids = res.data.res
                        }
                    } else {
                        this.$message({
                            message: res.data.msg + "err",
                            type: 'warning'
                        });
                    }
                })
        },
        // 验证文件路径
        isValidFilePath(filePath) {
            // Regular expressions for Windows and Unix file paths
            const windowsPathRegex = /^[a-zA-Z]:((\\)[\S].+\s?)*\\$/;
            const unixPathRegex = /^\/([\u4E00-\u9FA5A-Za-z0-9_]+\/{1})+$/;

            // Check if the file path matches either the Windows or Unix regex
            return windowsPathRegex.test(filePath) || unixPathRegex.test(filePath);
        },

        // 获取appname
        getappname() {
            let url = '/getappnamebyappid';
            let param = {
                "appid": Number(this.appid),
            }
            apiClient.post(url, param).then(res => {
                if (res.data.code == 0) {
                    this.appname = res.data.data;
                } else {
                    this.$message({
                        message: res.data.msg,
                        type: 'warning'
                    });
                }
            })
        },
    },
    mounted() {
        this.hostname = this.$store.state.ExecMsg.hostname;
        this.companyname = this.$store.state.ExecMsg.companyname;
        this.appid = this.$store.state.ExecMsg.appid;
        this.viewdataid = this.$store.state.Viewdataid.viewdataid;
        this.guid = this.$store.state.ExecMsg.guid;
        this.getPids();
        this.getappname();
    },
}
</script>

<style scoped lang="scss">
.el-col {
    padding: 5px;
    //margin-bottom: 1px;
}
</style>