﻿import Vue from 'vue'
import Vuex from 'vuex'
import ChinaMap from './modules/ChinaMap'
import ExecMsg from './modules/ExecMsg'
import CurPage from './modules/CurPage'
import S4info from './modules/S4info'
import Slminfo from './modules/Slminfo'
import Viewdataid from './modules/Viewdataid'
import TaskInfo from './modules/TaskInfo'
import createPersistedState from 'vuex-persistedstate';

Vue.use(Vuex)

export default new Vuex.Store({
  // 采用模块化管理vuex
  modules: {
    ChinaMap,
    ExecMsg,
    CurPage,
    S4info,
    Slminfo,
    Viewdataid,
    TaskInfo
  },
  plugins: [createPersistedState({
    storage: window.sessionStorage
  })]
})
