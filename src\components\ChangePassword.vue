<template>
	<div class="login">
		<el-form ref="changePasswordForm" :model="changePasswordForm" :rules="changePasswordRules" class="login-form">
			<h1 class="title"><img 
				src="../img/apm.png" style="height: 100%;"><span>APM</span>
			</h1>
			<el-form-item prop="username">
				<el-input v-model="changePasswordForm.username" type="text" auto-complete="off" placeholder="用户名">
					<i slot="prefix" class="el-icon-user"></i>
				</el-input>
			</el-form-item>
			<el-form-item prop="oldPassword">
				<el-input v-model="changePasswordForm.oldPassword" type="password" auto-complete="off" placeholder="原密码">
					<i slot="prefix" class="el-icon-key"></i>
				</el-input>
			</el-form-item>
			<el-form-item prop="newPassword">
				<el-input v-model="changePasswordForm.newPassword" type="password" auto-complete="off" placeholder="新密码"
					@keyup.enter.native="handleChangePassword">
					<i slot="prefix" class="el-icon-key"></i>
				</el-input>
			</el-form-item>
			<el-form-item prop="confirmPassword">
				<el-input v-model="changePasswordForm.confirmPassword" type="password" auto-complete="off" placeholder="确认新密码"
					@keyup.enter.native="handleChangePassword">
					<i slot="prefix" class="el-icon-key"></i>
				</el-input>
			</el-form-item>
			<el-form-item style="width: 100%">
				<el-button :loading="loading" size="medium" type="primary" style="width: 100%"
					@click.native.prevent="handleChangePassword">
					<span v-if="!loading">修 改 密 码</span>
					<span v-else>修 改 中...</span>
				</el-button>
			</el-form-item>
			<el-form-item style="width: 100%; text-align: right;">
				<el-link type="primary" @click="goBack">返回登录</el-link>
			</el-form-item>
		</el-form>
		<!--  底部  -->
		<div class="el-login-footer">
			<span>北京深盾科技股份有限公司 © 京ICP备16009104号京公网安备 11010802025663号</span>
		</div>
	</div>
</template>

<script>
import axios, { serverConfig } from '@/api/axios.js'
export default {
	name: "ChangePassword",
	data() {
		// 验证新密码和确认密码是否一致
		const validateConfirmPassword = (rule, value, callback) => {
			if (value !== this.changePasswordForm.newPassword) {
				callback(new Error('两次输入的密码不一致'));
			} else {
				callback();
			}
		};
		return {
			changePasswordForm: {
				username: "",
				oldPassword: "",
				newPassword: "",
				confirmPassword: "",
			},
			changePasswordRules: {
				username: [
					{ required: true, trigger: "blur", message: "请输入用户名" },
				],
				oldPassword: [
					{ required: true, trigger: "blur", message: "请输入原密码" },
				],
				newPassword: [
					{ required: true, trigger: "blur", message: "请输入新密码" },
					{ min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
				],
				confirmPassword: [
					{ required: true, trigger: "blur", message: "请确认新密码" },
					{ validator: validateConfirmPassword, trigger: 'blur' }
				],
			},
			loading: false,
		};
	},
	computed: {
		// 从全局配置获取服务器URL
		serverUrl() {
			const baseUrl = serverConfig.baseURL;
			// 从 baseURL 中提取服务器地址（去掉 /apm/v1 部分）
			return baseUrl.substring(0, baseUrl.lastIndexOf('/apm/v1'));
		}
	},
	methods: {
		handleChangePassword() {
			this.$refs.changePasswordForm.validate(valid => {
				if (valid) {
					this.loading = true;
					let url = `${this.serverUrl}/changepwd`;
					let param = {
						'username': this.changePasswordForm.username,
						'oldpwd': this.senseEncryption(this.changePasswordForm.oldPassword),
						'newpwd': this.senseEncryption(this.changePasswordForm.newPassword)
					};
					axios.post(url, param)
						.catch(error => {
							this.$message.error("server:" + error.message);
							this.loading = false;
							return Promise.reject(error);
						})
						.then(res => {
							if (res && res.data && res.data.code === 0) {
								this.$message({
									message: "密码修改成功",
									type: 'success'
								});
								this.$router.push('/login')
							} else if (res) {
								this.$message({
									message: res.data.msg || "密码修改失败",
									type: 'warning'
								});
							}
							this.loading = false;
						});
				}
			});
		},
		goBack() {
			this.$router.push('/login')
		},
		//密码加密
		senseEncryption(password) {
			let Base64 = require('js-base64').Base64;
			var allData = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "!", "@", "#", "$", "%", "^", "&", "*", "(", ")"];
			var allDataLen = allData.length;
			var passwordArr = password.split("");  //原密码数组
			var passwordLen = passwordArr.length;
			var randomArr = [];  //随机数数组
			var finalArr = [];  //合并后的数组
			for (var i = 0; i < passwordLen; i++) {
				var rand = Math.floor(Math.random() * allDataLen);
				randomArr.push(allData[rand]);
			};
			for (var j = 0; j < passwordLen; j++) {
				finalArr.push(passwordArr[j]);
				if (j < passwordLen - 1) {
					finalArr.push(randomArr[j]);
				}
			};
			var finalPassword = finalArr.join("");
			finalPassword = Base64.encode(finalPassword);
			return finalPassword;
		},
	},
};
</script>

<style lang="scss" scoped>
div {
	box-sizing: border-box;
}

.login {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	width: 100vw;
	background-image: url("../img/login-background.jpg");
	background-size: cover;
}

.login h1 {
	padding: 0;
}

.title {
	font-size: 30px;
	margin: 0px auto 30px auto;
	text-align: center;
	color: #303133;
	display: flex;
	align-items: center;
	justify-content: center;

	img {
		width: 40px;
		margin-right: 10px;
	}
}

.login-form {
	border-radius: 6px;
	background: #ffffff;
	width: 400px;
	box-sizing: border-box;
	padding: 70px 35px 25px 35px;
	box-shadow: 0 9px 20px 0 rgba(0, 0, 0, 0.1);

	.el-input {
		height: 38px;

		input {
			height: 38px;
		}
	}

	.input-icon {
		height: 39px;
		width: 14px;
		margin-left: 2px;
	}
}

.el-login-footer {
	height: 40px;
	line-height: 40px;
	position: fixed;
	bottom: 0;
	width: 100%;
	text-align: center;
	color: #909399;
	font-family: Arial;
	font-size: 12px;
	letter-spacing: 1px;
}
</style> 