{"name": "vuetest", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^1.7.2", "core-js": "^3.8.3", "echarts": "4.9", "element-ui": "^2.15.14", "js-base64": "^3.7.7", "jsonwebtoken": "^9.0.2", "qs": "^6.12.3", "sass": "^1.77.6", "sass-loader": "^14.2.1", "vue": "^2.6.14", "vue-progress-path": "^0.0.2", "vue-router": "^3.6.5", "vue-simple-spinner": "^1.2.10", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}