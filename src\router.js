import Login from './components/Login.vue'
import Overview from './components/Overview.vue'
import Home from './components/Home.vue';
import ExceptionMessage from './components/ExceptionMessage.vue';
import PiracyConfrontation from './components/PiracyConfrontation.vue';
import SetUp from './components/SetUp.vue';
import BehaviorDiary from './components/BehaviorDiary.vue';
import CounterHistory from './components/CounterHistory.vue';
import ExecCounter from './components/ExecCounter.vue';
import PageNotFound from './components/PageNotFound.vue';
import ExecCounterOffline from './components/ExecCounterOffline.vue';
import DefinedHistory from './components/DefinedHistory.vue';
import ViewData from './components/ViewData.vue';
import TestPage from './components/TestPage.vue';
import S4info from './components/S4info.vue';
import Slminfo from './components/Slminfo.vue';
import TaskVerify from './components/TaskVerify.vue';
import AllViewdata from './components/AllViewdata.vue';
import Mapinfo from './components/Mapinfo.vue';
import ChangePassword from './components/ChangePassword.vue';

let routes = [
    {
        path: '/login',
        name: 'login',
        component: Login,
        hidden: true
    },
    {
        path: '/changepassword',
        name: 'changepassword',
        component: ChangePassword,
        hidden: true
    },
    {
        path: '/',
        redirect: '/login',
        hidden: true
    },
    {
        path: '/',
        component: Home,
        name: '',
        iconCls: 'fa fa-bar-chart', //图标样式class
        children: [
            { path: '/exceptionMessage', component: ExceptionMessage, name: 'ExceptionMessage'},  //异常消息
            { path: '/piracyConfrontation', component: PiracyConfrontation, name: 'PiracyConfrontation'},  //盗版对抗
            { path: '/overview', component: Overview, name: 'Overview'},  //概览
            { path: '/setUp', component: SetUp, name: 'SetUp'},  //系统配置
            { path: '/behaviorDiary', component: BehaviorDiary, name: 'BehaviorDiary'},  //行为日志
            { path: '/counterHistory', component: CounterHistory, name: 'CounterHistory'},  //反制历史记录
            { path: '/execCounter', component: ExecCounter, name: 'ExecCounter' }, //执行反制
            { path: '/execCounteroffline', component: ExecCounterOffline, name: 'ExecCounterOffline' }, //执行反制(离线)
            { path: '/definedhistory', component: DefinedHistory, name: 'DefinedHistory' }, //执行反制(离线)
            { path: '/viewdata', component: ViewData, name: 'ViewData' }, // 发现盗版
            { path: '/testpage', component: TestPage, name: 'TestPage' }, //执行反制(离线)
            { path: '/s4info', component: S4info, name: 's4info' }, //s4信息
            { path: '/slminfo', component: Slminfo, name: 'slminfo' }, //slm信息
            { path: '/taskverify', component: TaskVerify, name: 'taskverify' }, //task信息
            { path: '/allviewdata', component: AllViewdata, name: 'allviewdata' }, //所有viewdata信息
            { path: '/mapinfo', component: Mapinfo, name: 'Mapinfo' }
        ]
    },
    // 404路由配置
    {
        path: '*',
        component: PageNotFound,
    },
]

export default routes;