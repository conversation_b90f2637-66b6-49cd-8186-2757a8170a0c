import Vue from 'vue'
import App from './App.vue'
import Vuex from 'vuex'
import './styles/common.scss'
import ElementUI from 'element-ui'
import routes from './router'
import './assets/icon/iconfont.css'
import VueRouter from 'vue-router'
import store from './store/index'
import 'element-ui/lib/theme-chalk/index.css'
import 'vue-progress-path/dist/vue-progress-path.css'
import VueProgress from 'vue-progress-path'
import * as echarts from 'echarts'; //引入echarts
import directive from './utils/directive';
Vue.use(directive)
Vue.prototype.$echarts = echarts //引入组件
// const jwt = require('jsonwebtoken');
// const secretKey = 'sensesecret'; 
Vue.use(ElementUI)
Vue.use(VueRouter)
Vue.use(Vuex)
Vue.use(VueProgress)
Vue.directive('removeAriaHidden', {
  bind(el, binding) {
    const ariaEls = el.querySelectorAll('.el-radio__original')
    ariaEls.forEach((item) => {
      item.removeAttribute('aria-hidden')
    })
  }
})

// 路由跳转时每次都定位到页面最上方
export const router = new VueRouter({
  mode: 'history',
  routes,
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 };
  }
})

router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('jwt');

  if (to.path !== '/login' && to.path !== '/changepassword' && !token) {
    console.log('token is nil');
    
    next('/login');
  }else{
    next();
  }
})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')