<template>
	<div>
		<div class="sense-main" style="padding-top: 0;">
			<div class="sense-header-tip">
				<p>• 盗版对抗提供了平台根据预设的盗版特征分析得出的所有盗版软件，对于存疑的可以点击机器ID，查看历史行为记录；</p>
				<p>• 对于希望针对终端软件进行反制措施的，可以点击执行反制实施具体的反制措施，平台提供了两种默认的反制措施："消息通知"、"推送反黑数据库"；</p>
				<p>• 当前执行反制措施处于公测阶段，不会收取任何费用。</p>
			</div>
			<div class="sense-header-link text-right">
				<router-link tag="span" to="/counterHistory">反制历史记录</router-link>
			</div>
			<div class="sense-table-list sense-table-ellipsis">
				<el-table :data="piracyListData" class="machineId-copy-box" border>
					<el-table-column label="机器ID" min-width="16%" show-overflow-tooltip>
						<template slot-scope="scope">
							<span class="table-link sense-ellipsis machineId-copy-val" 
								:title="scope.row.machineId" 
								@click="goNewPage(scope.row)">
								{{ scope.row.machineId }}
							</span>
							<a class="ssicon ss-copy machineId-copy" href="javascript:;" title="复制" 
								v-clipboard:copy="scope.row.machineId" 
								v-clipboard:success="copySuccess"></a>						
						</template>
					</el-table-column>	
					<el-table-column prop="pcName" label="机器名称" min-width="12%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="pcMac" label="MAC地址" min-width="11%" show-overflow-tooltip></el-table-column>				
					<el-table-column prop="name" label="软件名称" min-width="10%" show-overflow-tooltip></el-table-column>
					<el-table-column :formatter="areaFormatter" label="省/市" min-width="8%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="reason" label="判定理由" min-width="11%" show-overflow-tooltip></el-table-column>	
					<el-table-column prop="regTime" label="终端登记时间" min-width="13%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="reportTime" label="上报时间" min-width="13%"></el-table-column>
					<el-table-column label="操作" min-width="6%">
						<template slot-scope="scope">
							<a href="javascript:;" class="operate-text" @click="counterSystem(scope.row)">执行反制</a>
				        </template>
					</el-table-column>			
				</el-table>
			</div>
			<div class="sense-table-pager" v-if="total>0">
				<el-pagination
					background
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="currentPage"
					:page-sizes="[10,20,30]"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total">
				</el-pagination>
			</div>
		</div>
		<div class="all-dialog">
			<!-- 执行反制 start -->
			<el-dialog class="sense-dialog" title="执行反制" :visible.sync="counterSystemFormVisible" :before-close="counterSystemClose">
				<el-form :model="counterSystemForm" :rules="counterSystemFormRule" ref="counterSystemForm" size="small" label-width="100px">
					<el-form-item label="反制措施" prop="measure">
						<el-select v-model="counterSystemForm.measure" @change="measureChange" auto-complete="off">
							<el-option label="消息通知" value="1"></el-option>
							<el-option label="推送反黑数据库" value="2"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="消息模板" prop="template" 
						v-show="counterSystemForm.measure == 1"
						:rules="this.counterSystemForm.measure == 1?counterSystemFormRule.template:[{required: false, message: '请选择消息模板', trigger: 'change'}]">
						<el-select auto-complete="off" v-model="counterSystemForm.template" filterable>
							<el-option v-for="item in templateList" :label="item.title" :value="item.guid" :key="item.guid"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="反黑数据库" prop="db" 
						v-show="counterSystemForm.measure == 2"
						:rules="this.counterSystemForm.measure == 2?counterSystemFormRule.db:[{required: false, message: '请选择反黑数据库', trigger: 'change'}]">
						<el-select v-model="counterSystemForm.db" auto-complete="off">
							<el-option v-for="item in dbList" :label="item.name" :value="item.guid" :key="item.guid"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="措施描述" prop="desc">
						<el-input type="textarea" v-model="counterSystemForm.desc" placeholder="措施描述(500字以内)" auto-complete="off"></el-input>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button size="small" @click="counterSystemClose">取 消</el-button>
					<el-button type="primary" size="small" @click="counterSystemFormSubmit">确 定</el-button>
				</div>
			</el-dialog>
			<!-- 执行反制 end -->
		</div>
	</div>
</template>

<script>

	export default {
		data() {
			return {
				isPreview: '',
				previewObj: {
					piracyListData: [
						{machineId: '10349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'word.exe', 'province': '黑龙江', 'city': '大庆', 'reason': '检测到word.exe的反黑启动失败，返回:0x102', 'pcName': 'sensetest-admin1', 'pcMac': '34:12:f8:e3:80:ed', 'regTime': '2020-01-01 12:05:35', 'reportTime': '2020-01-01 12:05:35'},
						{machineId: '11349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'photoshop.exe', 'province': '北京', 'city': '北京', 'reason': '检测到photoshop.exe的内存中包含恶意特征ID:0x18', 'pcName': 'sensetest-admin2', 'pcMac': '56:12:f8:6e:80:ed', 'regTime': '2019-12-25 09:05:35', 'reportTime': '2019-12-25 09:05:35'},
						{machineId: '12349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'excel.exe', 'province': '浙江', 'city': '杭州', 'reason': '检测到excel.exe的反黑启动失败，返回:0x108', 'pcName': 'sensetest-admin3', 'pcMac': 'e4:12:f8:e3:86:ed', 'regTime': '2019-12-24 17:05:35', 'reportTime': '2019-12-24 17:05:35'},
						{machineId: '13349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'word.exe', 'province': '河南', 'city': '郑州', 'reason': '检测到word.exe的反黑启动失败，返回:0x105', 'pcName': 'sensetest-admin4', 'pcMac': 'f2:12:f4:e3:80:ed', 'regTime': '2019-12-16 09:05:35', 'reportTime': '2019-12-16 09:05:35'},
						{machineId: '14349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'Foxmail.exe', 'province': '陕西', 'city': '西安', 'reason': '检测到Foxmail.exe的EnumDevice函数被挂钩', 'pcName': 'sensetest-admin5', 'pcMac': '25:12:ff:e3:80:ed', 'regTime': '2019-12-12 20:07:35', 'reportTime': '2019-12-12 20:07:35'},
						{machineId: '15349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'word.exe', 'province': '黑龙江', 'city': '大庆', 'reason': '检测到word.exe的反黑启动失败，返回:0x102', 'pcName': 'sensetest-admin6', 'pcMac': 'f5:12:f8:e3:80:ed', 'regTime': '2019-12-08 11:05:35', 'reportTime': '2019-12-08 11:05:35'},
						{machineId: '16349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'photoshop.exe', 'province': '北京', 'city': '北京', 'reason': '检测到photoshop.exe的内存中包含恶意特征ID:0x18', 'pcName': 'sensetest-admin7', 'pcMac': 'e8:1e:f8:e3:80:ed', 'regTime': '2019-12-07 12:55:35', 'reportTime': '2019-12-07 12:55:35'},
						{machineId: '17349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'excel.exe', 'province': '浙江', 'city': '杭州', 'reason': '检测到excel.exe的反黑启动失败，返回:0x108', 'pcName': 'sensetest-admin8', 'pcMac': '27:1f:f8:e3:80:ed', 'regTime': '2019-12-07 10:05:35', 'reportTime': '2019-12-07 10:05:35'},
						{machineId: '18349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'word.exe', 'province': '河南', 'city': '郑州', 'reason': '检测到word.exe的反黑启动失败，返回:0x105', 'pcName': 'sensetest-admin9', 'pcMac': 'f1:12:f8:e3:80:ed', 'regTime': '2019-12-01 12:05:45', 'reportTime': '2019-12-01 12:05:45'},
						{machineId: '19349af91a1848b69a1e16086b79bb6f0480b784c1a14bc3bc4ee7451afcd7cd', 'name': 'Foxmail.exe', 'province': '陕西', 'city': '西安', 'reason': '检测到Foxmail.exe的EnumDevice函数被挂钩', 'pcName': 'sensetest-admin10', 'pcMac': 'b6:12:f8:e3:80:ed', 'regTime': '2019-11-24 03:05:35', 'reportTime': '2019-11-24 03:05:35'},
					],
					total: 10,
					templateList: [
						{'title': '消息模板-盗版软件提示', 'guid': '100001'},
						{'title': '消息模板-可疑行为提示', 'guid': '100002'}
					],
					dbList: [
						{'name': 'ahsDB_0800000000000160_1.1.1.9014.db', 'guid': '200001'},
						{'name': 'ahsDB_0800000000000161_1.1.1.9015.db', 'guid': '200002'}
					],
				},
				piracyListData: [],  //盗版对抗列表数组
				currentPage: 1,  //当前页
				pageSize: 10,  //每页条数
				total: 0,  //数据总数	
				counterSystemFormVisible: false,
				counterSystemForm: {
					measure: '',
					template: '',
					db: '',
					desc: ''
				},
				templateList: [],  //消息模板列表
				dbList: [],  //反黑DB列表
				//规则校验
				counterSystemFormRule: {
					measure: [
						{ required: true, message: '请选择反制措施', trigger: 'change' },
					],
					template: [
						{ required: true, message: '请选择消息模板', trigger: 'change' },
					],
					db: [
						{ required: true, message: '请选择反黑数据库', trigger: 'change' },
					],
					desc: [
						{ max: 500, message: '措施描述长度不能超过500个字符', trigger: 'change' }
					],
				}
			}
		},
		methods: {
			getPiracyList() {
				if (this.isPreview == 1) {
					this.piracyListData = this.previewObj.piracyListData;
					this.total = this.previewObj.total;
				} else {
				};										
			},
			//格式化省市
			areaFormatter(row, index) {
				return row.province + '-' + row.city;
			},		
			//切换每页显示条数
			handleSizeChange(val) {
				this.pageSize = val;
				this.getPiracyList();
			},
			//切换当前页
			handleCurrentChange(val) {
				this.currentPage = val;
				this.getPiracyList();
			},
			//点击机器ID跳转到行为日志页面
			goNewPage(row) {
				this.$router.push({
	          		path: '/behaviorDiary',
		            query: {
		            	machineId: row.machineId,
		            	id: row.id
		            }
	        	})
			},
			//执行反制
			counterSystem(row) {
				this.counterSystemFormVisible = true;
				this.counterSystemForm = {
					machineId: row.machineId,
					exeName: row.exeName,
					measure: '',
					template: '',
					db: '',
					desc: ''
				};

				//获取消息模板下拉列表
				this.loadTemplateList();
				//获取反黑数据库下拉列表
				this.loadDbList();
			},
			//获取消息模板下拉列表
			loadTemplateList(searchVal) {
				if (this.isPreview == 1) {
					this.templateList = this.previewObj.templateList;
				} else {
				};
			},
			//获取反黑数据库下拉列表
			loadDbList() {
				if (this.isPreview == 1) {
					this.dbList = this.previewObj.dbList;
				} else {
				};
			},
			//反制措施change
			measureChange() {
				this.counterSystemForm.template = '';
				this.counterSystemForm.db = '';
			},
			//执行反制提交
			counterSystemFormSubmit() {
				this.$refs.counterSystemForm.validate((valid) => {
					if (valid) {
						if (this.isPreview == 1) {
							this.counterSystemFormVisible = false;
							this.$message({
								message: '预览状态下无法完全模拟真实场景，请使用账号登录后操作',
								type: 'warning'
							});
						} else {	
						};								
					} else {
						return false;
					}
				});
			},
			//弹窗关闭
			counterSystemClose() {
		    	this.$refs['counterSystemForm'].resetFields();
		    	this.counterSystemFormVisible = false;
		    },
		    //复制机器ID成功
		    copySuccess() {
		    	this.$message({
					message: '机器ID复制成功',
					type: 'success'
				});
		    }
		},
		mounted() {
			this.isPreview = sessionStorage.getItem('isPreview');
			this.getPiracyList();
		},
	}
</script>

<style scoped lang="scss">	

</style>