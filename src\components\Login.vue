<template>
	<div class="login">
		<el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
			<h1 class="title"><img 
				src="../img/apm.png" style="height: 100%;"><span>APM</span>
			</h1>
			<el-form-item prop="username">
				<el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
					<i slot="prefix" class="el-icon-user"></i>
				</el-input>
			</el-form-item>
			<el-form-item prop="password">
				<el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
					@keyup.enter.native="handleLogin">
					<i slot="prefix" class="el-icon-key"></i>
				</el-input>
			</el-form-item>
			<!-- <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">记住密码</el-checkbox> -->
			<el-form-item style="width: 100%">
				<el-button :loading="loading" size="medium" type="primary" style="width: 100%"
					@click.native.prevent="handleLogin">
					<span v-if="!loading">登 录</span>
					<span v-else>登 录 中...</span>
				</el-button>
			</el-form-item>
			<el-form-item style="width: 100%; text-align: right;">
				<el-link type="primary" @click="goToChangePassword">修改密码</el-link>
			</el-form-item>
		</el-form>
		<!--  底部  -->
		<div class="el-login-footer">
			<span>北京深盾科技股份有限公司 © 京ICP备16009104号京公网安备 11010802025663号</span>
		</div>
	</div>
</template>

<script>
import axios, { serverConfig } from '@/api/axios.js'
export default {
	name: "Login",
	data() {
		return {
			loginForm: {
				username: "",
				password: "",
			},
			loginRules: {
				username: [
					{ required: true, trigger: "blur", message: "请输入账号" },
				],
				password: [
					{ required: true, trigger: "blur", message: "请输入密码" },
				],
			},
			loading: false,
		};
	},
	computed: {
		// 从全局配置获取服务器URL
		serverUrl() {
			const baseUrl = serverConfig.baseURL;
			// 从 baseURL 中提取服务器地址（去掉 /apm/v1 部分）
			return baseUrl.substring(0, baseUrl.lastIndexOf('/apm/v1'));
		}
	},
	methods: {
		handleLogin() {
			this.loading = true;
			let url = `${this.serverUrl}/login`;
			let param = {
				'username': this.loginForm.username,
				'password': this.senseEncryption(this.loginForm.password)
			};
			axios.post(url, param)
				.catch(error => {
					this.loading = false;
					this.$message.error("server:" + error.message);
					return Promise.reject(error);
				})
				.then(res => {
					this.loading = false;
					if (res && res.data && res.data.code === 0) {
						localStorage.setItem('jwt', res.data.token);
						this.$router.push('/overview')
						this.$message({
							message: "登陆成功",
							type: 'success'
						});
					} else if (res) {
						this.$message({
							message: "warning:账号或密码错误",
							type: 'warning'
						});
					}
				});
		},
		goToChangePassword() {
			this.$router.push('/changepassword')
		},
		//密码加密
		senseEncryption(password) {
			let Base64 = require('js-base64').Base64;
			var allData = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "!", "@", "#", "$", "%", "^", "&", "*", "(", ")"];
			var allDataLen = allData.length;
			var passwordArr = password.split("");  //原密码数组
			var passwordLen = passwordArr.length;
			var randomArr = [];  //随机数数组
			var finalArr = [];  //合并后的数组
			for (var i = 0; i < passwordLen; i++) {
				var rand = Math.floor(Math.random() * allDataLen);
				randomArr.push(allData[rand]);
			};
			for (var j = 0; j < passwordLen; j++) {
				finalArr.push(passwordArr[j]);
				if (j < passwordLen - 1) {
					finalArr.push(randomArr[j]);
				}
			};
			var finalPassword = finalArr.join("");
			finalPassword = Base64.encode(finalPassword);
			return finalPassword;
		},
	},
};
</script>

<style lang="scss" scoped>
div {
	box-sizing: border-box;
}

.login {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	width: 100vw;
	background-image: url("../img/login-background.jpg");
	background-size: cover;
}

.login h1 {
	padding: 0;
}

.title {
	font-size: 30px;
	margin: 0px auto 30px auto;
	text-align: center;
	color: #303133;
	display: flex;
	align-items: center;

	justify-content: center;

	img {
		width: 40px;
		margin-right: 10px;
	}
}

.login-form {
	border-radius: 6px;
	background: #ffffff;
	width: 400px;
	box-sizing: border-box;
	padding: 70px 35px 25px 35px;
	box-shadow: 0 9px 20px 0 rgba(0, 0, 0, 0.1);

	.el-input {
		height: 38px;

		input {
			height: 38px;
		}
	}

	.input-icon {
		height: 39px;
		width: 14px;
		margin-left: 2px;
	}
}

.el-login-footer {
	height: 40px;
	line-height: 40px;
	position: fixed;
	bottom: 0;
	width: 100%;
	text-align: center;
	color: #909399;
	font-family: Arial;
	font-size: 12px;
	letter-spacing: 1px;
}
</style>