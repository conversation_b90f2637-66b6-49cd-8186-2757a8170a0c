<template>
	<div>
		<div class="sense-main">
			<div class="sense-table-list sense-table-ellipsis">
				<!-- 
					数据展示
					    - hostname
						- guid
						- 公司名称
						- 软件名称
						- 发送状态
						- 执行状态
						- 发送时间
						- 发送人
						- 打击详情
						- 操作
				-->
				<!-- 查询数据 
				 		- 时间范围
				 		- 发送状态
				 		- 执行状态
				 		- guid
				 		- 软件名称
				 		- 是否在线打击
				-->
				<div>
					<span>起始时间 : </span>
					<el-date-picker v-model="startTime" size="mini" type="datetime" style="width: 180px;"
						placeholder="选择起始时间" :picker-options="pickerOptions" />
					<span style="margin-left: 5px;">结束时间 : </span>
					<el-date-picker v-model="endTime" size="mini" type="datetime" style="width: 180px;"
						placeholder="选择结束时间" :picker-options="pickerOptions" />
					<span style="margin-left: 5px;">到达状态 : </span>
					<el-select v-model="arrivestatus" clearable placeholder="请选择" style="width: 90px;" size="small">
						<el-option label="已发布" value="0"></el-option>
						<el-option label="已送达" value="1"></el-option>
						<el-option label="已撤销" value="2"></el-option>
					</el-select>
					<span style="margin-left: 5px;">执行状态 : </span>
					<el-select v-model="executestatus" clearable placeholder="请选择" style="width: 90px;" size="small">
						<el-option label="未执行" value="0"></el-option>
						<el-option label="已执行" value="1"></el-option>
						<el-option label="执行失败" value="2"></el-option>
						<el-option label="已撤销" value="3"></el-option>
					</el-select>
					<span style="margin-left: 5px;">机器唯一ID : </span>
					<el-input v-model="guid" placeholder="请输入" style="width: 250px;" size="small"
						@keyup.enter.native="queryData" />
					<span style="margin-left: 5px;">软件名称 : </span>
					<el-select v-model="appid" clearable placeholder="请选择" style="width: 120px;" size="small">
						<el-option v-for="item in appnamelist" :key="item.appid" :label="item.appname"
							:value="item.appid"> </el-option>
					</el-select>
					<span style="margin-left: 5px;">是否在线打击 : </span>
					<el-select v-model="isonline" placeholder="请选择" clearable style="width: 85px;" size="small">
						<el-option label="全部" value="0"></el-option>
						<el-option label="在线" value="1"></el-option>
						<el-option label="离线" value="2"></el-option>
					</el-select>
					<el-button style="margin-left: 10px;" size="small" round type="primary" icon="el-icon-search"
						@click="getdefinedhistory(1, pageSize)">搜索</el-button>
					<br><br>
				</div>
				<el-table :cell-style="{ padding: '7px' }" :data="exceptionListData" border>
					<el-table-column prop="hostname" width="130"  label="Hostname" show-overflow-tooltip></el-table-column>
					<el-table-column prop="guid" label="机器唯一ID" width="320" show-overflow-tooltip></el-table-column>
					<el-table-column prop="companyname" label="公司名称" width="280" show-overflow-tooltip></el-table-column>
					<el-table-column prop="appname" label="app名称" width="110" show-overflow-tooltip></el-table-column>
					<el-table-column label="到达状态" width="100">
						<template slot-scope="scope">
							<span v-if="scope.row.arrivestatus == 0" class="sense-mestype sense-mestype-3">已发布</span>
							<span v-if="scope.row.arrivestatus == 1" class="sense-mestype sense-mestype-5">已送达</span>
							<span v-if="scope.row.arrivestatus == 2" class="sense-mestype sense-mestype-0">已撤销</span>
						</template>
					</el-table-column>
					<el-table-column label="执行状态" width="100">
						<template slot-scope="scope">
							<span v-if="scope.row.executestatus == 0" class="sense-mestype sense-mestype-0">未执行</span>
							<span v-if="scope.row.executestatus == 1" class="sense-mestype sense-mestype-5">已执行</span>
							<el-link v-if="scope.row.executestatus == 2" :underline="false"
								class="sense-mestype sense-mestype-2" @click="definederrorinfo(scope.row)"
								type="primary">执行失败</el-link>
							<span v-if="scope.row.executestatus == 3" class="sense-mestype sense-mestype-1">已撤销</span>
							<el-dialog title="失败信息" :visible.sync="dialogErrorinfoFormVisible">
								<el-descriptions :column=1>
									<el-descriptions-item label="Error code">{{ errorcode }}</el-descriptions-item>
									<el-descriptions-item label="Error message">{{ errormsg }}</el-descriptions-item>
								</el-descriptions>
								<div slot="footer" class="dialog-footer">
									<el-button type="primary" @click="dialogErrorinfoFormVisible = false">确
										定</el-button>
								</div>
							</el-dialog>
						</template>
					</el-table-column>
					<el-table-column prop="defeattime" label="下发时间" width="180" show-overflow-tooltip></el-table-column>
					<el-table-column prop="username" label="操作用户" show-overflow-tooltip></el-table-column>
					<el-table-column prop="isonline" label="打击类型" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="scope.row.isonline == true">在线打击</span>
							<span v-if="scope.row.isonline == false">离线打击</span>
						</template>
					</el-table-column>
					<el-table-column label="下发指令">
						<template slot-scope="scope">
							<el-button @click="dialogClick(scope.row)" type="text">详细信息</el-button>
							<el-dialog title="详细信息" style="width: 600px;margin-left: 30%;"
								:visible.sync="dialogFormVisible">
								<span>电子取证</span>
								<i v-if="check1" style="margin-left: 2%;" class="el-icon-circle-check"></i>
								<br>
								<span>弹窗</span>
								<i v-if="check2" style="margin-left: 2%;" class="el-icon-circle-check"></i>
								<br>
								<span>延迟退出</span>
								<i v-if="check3" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>锁定桌面</span>
								<i v-if="check4" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>注销登录</span>
								<i v-if="check5" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>锁定文件</span>
								<i v-if="check6" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>锁定程序</span>
								<i v-if="check7" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>关闭句柄</span>
								<i v-if="check8" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>自锁-用户级</span>
								<i v-if="check9" style="margin-left: 2%;" class="el-icon-circle-check"></i><br>
								<span>自锁-系统级</span>
								<i v-if="check10" style="margin-left: 2%;" class="el-icon-circle-check"></i><br><br>
								<span>APPDATA取证目录 : </span>
								<span v-if="dialogcompanyname != ''">{{ dialogcompanyname }}</span>
								<span v-else>空</span><br>
								<span>弹窗标题 : </span>
								<span v-if="dialogmessagetitle != ''">{{ dialogmessagetitle }}</span>
								<span v-else>空</span><br>
								<span>弹窗内容 : </span>
								<span v-if="dialogmessagetext != ''">{{ dialogmessagetext }}</span>
								<span v-else>空</span><br>
								<span>锁定程序名称 : </span>
								<span v-if="dialogifeo != ''">{{ dialogifeo }}</span>
								<span v-else>空</span><br>
								<span>延迟退出时间 : </span>
								<span v-if="dialogexitdelaytime != 0">{{ dialogexitdelaytime }}</span>
								<span v-else>空</span><br>
								<span>锁定文件后缀 : </span>
								<span v-if="dialoglockfile != ''">{{ dialoglockfile }}</span>
								<span v-else>空</span><br>

								<div slot="footer" class="dialog-footer">
									<el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
								</div>
							</el-dialog>
						</template>
					</el-table-column>
					<el-table-column label="措施" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="scope.row.executestatus == 0" class="ssicon ss-revoke operate-icon" title="撤销"
								@click="revokeHistory(scope.row)"></span>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="sense-table-pager" v-if="total > 0">
				<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:current-page="currentPage" :page-sizes="[10, 20, 30]" :page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</div>
		<div class="all-dialog">

		</div>
	</div>
</template>

<script>
import apiClient from '../api/axios'
export default {
	data() {
		return {
			// 查询数据
			startTime: '',
			endTime: '',
			arrivestatus: '',
			executestatus: '',
			guid: '',
			appid: '',
			isonline: '',
			appnamelist: [],
			check1: false,
			check2: false,
			check3: false,
			check4: false,
			check5: false,
			check6: false,
			check7: false,
			check8: false,
			check9: false,
			check10: false,
			dialogcompanyname: '',
			dialogmessagetitle: '',
			dialogmessagetext: '',
			dialogifeo: '',
			dialogexitdelaytime: '',
			dialoglockfile: '',
			errorcode: 0,
			errormsg: '',
			pickerOptions: {
				disabledDate(time) {
					// 禁止选择未来时间
					return time.getTime() > Date.now();
				}
			},

			dialogFormVisible: false,
			dialogErrorinfoFormVisible: false,
			isPreview: '',
			exceptionListData: [],  // 展示数据
			currentPage: 1,  //当前页
			pageSize: 10,  //每页条数
			total: 0,  //数据总数	
			Dialogdata: {}, // 存储app详细信息
		}
	},
	computed: {
		currentPageData() {
			const start = (this.currentPage - 1) * this.pageSize;
			const end = start + this.pageSize;
			this.getdefinedhistory();
		}
	},
	methods: {
		revokeHistory(row) {
			this.$confirm('确认撤销吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let url = '/revokedefeat';
				let param = {
					"taskid": row.taskid,
				}
				apiClient.post(url, param).then(res => {
					if (res.data.code == 0) {
						this.$message.success('撤销成功');
						this.getdefinedhistory(this.currentPage, this.pageSize);
					}else{
						this.$message.error('撤销失败:', res.data.msg);
					}
				})
			}).catch(() => {
				this.$message.warning('取消撤销');
			});
		},
		// 执行失败的错误信息
		definederrorinfo(row) {
			this.dialogErrorinfoFormVisible = true;
			this.errorcode = row.errCode;
			this.errormsg = row.errMsg;
		},
		//切换每页显示条数
		handleSizeChange(val) {
			this.pageSize = val;
			this.getdefinedhistory(this.currentPage, val);
		},
		//切换当前页
		handleCurrentChange(val) {
			this.currentPage = val;
			this.getdefinedhistory(val, this.pageSize);
		},
		// 详细信息
		dialogClick(row) {
			let optlen = row.defeatoptions.length;
			// 清空
			for (let index = 1; index <= 10; index++) {
				this['check' + index.toString()] = false;
			}
			// 赋值
			for (let i = row.defeatoptions.length - 1; i >= 0; i--) {
				if (row.defeatoptions[i] === '1') {
					let num = (optlen - i).toString();
					let chi = 'check' + num;
					this[chi] = true;
				}
			}
			this.dialogcompanyname = row.spdefeat.companyname;
			this.dialogmessagetitle = row.spdefeat.messagetitle;
			this.dialogmessagetext = row.spdefeat.messagetext;
			this.dialogifeo = row.spdefeat.ifeo;
			this.dialogexitdelaytime = row.spdefeat.exitdelaytime;
			this.dialoglockfile = row.spdefeat.lockfile;
			this.dialogFormVisible = true;
		},
		getdefinedhistory(page, size) {
			let url = '/lic/getalldefeathistory';
			let param = {
				"page": page,
				"limit": size,
			}
			if (this.startTime != '') {
				param.starttime = this.startTime;
			}
			if (this.endTime != '') {
				param.endtime = this.endTime;
			}
			if (this.arrivestatus != '') {
				param.arrivestatus = Number(this.arrivestatus);
			}
			if (this.executestatus != '') {
				param.executestatus = Number(this.executestatus);
			}
			if (this.guid != '') {
				param.guid = this.guid;
			}
			if (this.appid != '') {
				param.appid = this.appid;
			}
			if (this.isonline != '') {
				if (this.isonline != '0') {
					param.isonline = this.isonline == '1' ? true : false;
				}
			}

			apiClient.post(url, param).catch(error => {
				this.$message.error("server:502:" + error.message);
				return
			})
				.then(res => {
					if (res.data.code == 0) {
						// 获取数据
						if (res.data.results == null) {
							this.exceptionListData = [];
							this.total = 0;
						} else {
							this.exceptionListData = res.data.results;
							this.total = res.data.total;
						}
						this.$message({
							message: "查询成功",
							type: 'success',
                            duration: 1000
						})
					} else {
						this.$message({
							message: res.data.msg,
							type: 'warning'
						});
					}
				})
		},

		//  获取appname
		getappname() {
			let url = '/getallappid2name';
			apiClient.get(url).catch(error => {
				this.$message.error("server:502:" + error.message);
				return
			})
				.then(res => {
					if (res.data.code == 0) {
						if (res.data.data == null) {
							this.appnamelist = [];
						} else {
							this.appnamelist = res.data.data;
						}
					} else {
						this.$message({
							message: res.data.msg,
							type: 'warning'
						})
					}
				})
		}
	},
	mounted() {
		this.getdefinedhistory(this.currentPage, this.pageSize);
		this.getappname();
	},
}
</script>

<style scoped lang="scss"></style>