<template>
    <div>
        <div class="sense-main">
            <div class="sense-table-list sense-table-ellipsis">
                <template>
                    <!-- <el-button type="primary" @click="getMap">map</el-button> -->
                    <div>
                        <span>IP : </span>
                        <el-input v-model="ipinput" size="small" style="width: 130px;" placeholder="请输入IP"></el-input>
                        <span> 域名 : </span>
                        <el-input v-model="domaininput" size="small" style="width: 130px;"
                            placeholder="请输入域名"></el-input>
                        <span> 盗版类型 : </span>
                        <el-select v-model="piracyvalue" size="small" collapse-tags multiple clearable
                            style="width: 150px;" placeholder="请选择">
                            <el-option v-for="item in piracyoptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <span> 锁类型 : </span>
                        <el-select v-model="lockvalue" size="small" clearable style="width: 130px;" placeholder="请选择">
                            <el-option v-for="item in locktypeoptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <span> 锁号 : </span>
                        <el-input v-model="lockinput" size="small" style="width: 140px;" placeholder="请输入锁号"></el-input>
                        <span> 公司 : </span>
                        <el-select v-model="companyvalue" size="small" clearable collapse-tags multiple
                            style="width: 150px;" placeholder="请选择">
                            <el-option v-for="item in companynameoptions" :key="item.id" :label="item.companyname"
                                :value="item.id">
                            </el-option>
                        </el-select>
                        <span> 机器唯一ID : </span>
                        <el-input v-model="guidinput" size="small" style="width: 290px;" placeholder="请输入"></el-input>
                        <el-button style="margin-left: 10px;" type="primary" icon="el-icon-search" size="small"
                            @click="getviewdatabyfliter(1, fliterpagesize)">搜索</el-button>
                        <el-button style="margin-left: 10px;" type="primary" size="small"
                            @click="resetfliter">重置</el-button>
                    </div>
                    <br>
                    <el-button type="primary" size="small" icon="el-icon-location-information"
                        @click="multiplecompanyclick" round>公司多条定位</el-button>
                    <el-button type="warning" size="small" icon="el-icon-check"
                        @click="multiplePiracyVerifyClick" round>批量盗版复核</el-button>
                    <el-dialog title="公司多条定位" :visible.sync="multiplecompanyVisible" :modal="false" width="60%"
                        top="15%" @close="multiplecompanyclose">
                        <el-radio-group v-model="multiplecompanyradio" v-removeAriaHidden>
                            <el-radio :label=true>自动定位</el-radio>
                            <el-radio :label=false>选择定位</el-radio>
                            <el-select v-model="value" :disabled="multiplecompanyradio" placeholder="请选择">
                                <el-option v-for="item in companyoptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-radio-group>
                        <el-button type="primary" @click="multiplegetcompanyid"
                            style="margin-left: 5%;">开始定位</el-button>
                        <el-button type="primary" @click="multiplecompanymark" style="margin-left: 5%;">全部标记</el-button>
                        <br>
                        <br>
                        <el-table border style="width: 100rem" max-height="400" :cell-style="{ padding: '7px' }"
                            :data="multipleSelection">
                            <el-table-column align="center" header-align="center" prop="hostname" label="主机名称"
                                width="120" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="companyname" label="当前定位公司"
                                width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="iplocationhistory" label="城市"
                                width="150" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="appname" label="软件名称"
                                width="120" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" label="重新定位结果" width="320">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.selectcompanyid" style="width: 20rem;" multiple
                                        clearable collapse-tags placeholder="请选择">
                                        <el-option v-for="item in companynameoptions" :key="item.id"
                                            :label="item.companyname" :value="item.id">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                        </el-table>
                        <span slot="footer" class="dialog-footer">
                            <el-button @click="multiplecompanyVisible = false">取 消</el-button>
                            <el-button type="primary" @click="multiplecompanyVisible = false">确 定</el-button>
                        </span>
                    </el-dialog>
                    
                    <!-- 批量盗版复核对话框 -->
                    <el-dialog title="批量盗版复核" :visible.sync="multiplePiracyVerifyVisible" :modal="false" width="50%"
                        top="15%" @close="multiplePiracyVerifyClose">
                        <div>
                            <span>选择复核数量: </span>
                            <el-select v-model="verifyCountValue" placeholder="请选择复核数量">
                                <el-option v-for="item in verifyCountOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-button type="primary" @click="startMultipleVerify"
                                style="margin-left: 5%;">开始批量复核</el-button>
                        </div>
                        <br>
                        <br>
                        <el-table border style="width: 100%" max-height="400" :cell-style="{ padding: '7px' }"
                            :data="multipleVerifySelection">
                            <el-table-column align="center" header-align="center" prop="hostname" label="主机名称"
                                width="120" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="companyname" label="公司名称"
                                width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="guid" label="机器唯一ID"
                                width="310" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" prop="appname" label="软件信息"
                                width="120" show-overflow-tooltip></el-table-column>
                            <el-table-column align="center" header-align="center" label="复核状态" width="120">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.loading">
                                        <i class="el-icon-loading"></i>
                                    </span>
                                    <span v-else-if="scope.row.verifyStatus === 'success'" class="sense-mestype sense-mestype-5">复核成功</span>
                                    <span v-else-if="scope.row.verifyStatus === 'error'" class="sense-mestype sense-mestype-2">复核失败</span>
                                    <span v-else class="sense-mestype sense-mestype-3">未复核</span>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" header-align="center" label="操作" width="120">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="primary" @click="gotoSingleVerify(scope.row)">单独复核</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <span slot="footer" class="dialog-footer">
                            <el-button @click="multiplePiracyVerifyVisible = false">取 消</el-button>
                            <el-button type="primary" @click="multiplePiracyVerifyVisible = false">确 定</el-button>
                        </span>
                    </el-dialog>
                    <br><br>
                    
                    <!-- 添加加载动画 -->
                    <div v-if="loading" class="loading-container">
                        <i class="el-icon-loading"></i>
                        <p>数据加载中...</p>
                    </div>
                    
                    <!-- 表格内容 -->
                    <el-table v-else border style="width: 100%" max-height="600" @selection-change="handleSelectionChange"
                        :cell-style="{ padding: '7px' }" :data="exceptionListData">
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <!-- 盗版审核 -->
                        <el-table-column align="center" header-align="center" fixed label="审核盗版" width="130">
                            <template slot-scope="scope">
                                <span v-if="scope.row.piracytype == 0" class="sense-mestype sense-mestype-3">待复核</span>
                                <span v-if="scope.row.piracytype == -1"
                                    class="sense-mestype sense-mestype-2">正盗混用</span>
                                <span v-if="scope.row.piracytype == -2" class="sense-mestype sense-mestype-2">纯盗版</span>
                                <span v-if="scope.row.piracytype == -3"
                                    class="sense-mestype sense-mestype-2">滥用授权</span>
                                <span v-if="scope.row.piracytype == -4"
                                    class="sense-mestype sense-mestype-2">回收正版锁</span>
                                <span v-if="scope.row.piracytype == -5"
                                    class="sense-mestype sense-mestype-2">偷盗授权</span>
                                <span v-if="scope.row.piracytype == -7" class="sense-mestype sense-mestype-2">黑名单</span>
                                <span v-if="scope.row.piracytype == 1"
                                    class="sense-mestype sense-mestype-5">需人工排查</span>
                                <span v-if="scope.row.piracytype == 2"
                                    class="sense-mestype sense-mestype-5">Demo版本</span>
                                <span v-if="scope.row.piracytype == 3" class="sense-mestype sense-mestype-5">其他意见</span>
                                <span v-if="scope.row.piracytype == 255"
                                    class="sense-mestype sense-mestype-5">正版锁</span>
                            </template>
                        </el-table-column>
                        <!-- 主机名称 -->
                        <el-table-column align="center" header-align="center" prop="hostname" label="主机名称" width="135"
                            show-overflow-tooltip></el-table-column>
                        <!-- 公司名称 -->
                        <el-table-column align="center" header-align="center" prop="companyname" label="公司名称"
                            width="300" show-overflow-tooltip></el-table-column>
                        <!-- 城市与ISP -->
                        <el-table-column align="center" header-align="center" prop="iplocationhistory" label="城市"
                            width="135" show-overflow-tooltip></el-table-column>
                        <!-- 正版授权锁信息 -->
                        <el-table-column align="center" header-align="center" label="正版授权锁信息" width="190"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="scope.row.glockinfo === '空'">空</span>
                                <el-link v-else-if="scope.row.egcontainer === 1" type="primary"
                                    @click="gos4infog(scope.row.id)">{{ scope.row.glockinfo }}</el-link>
                                <el-link v-else-if="scope.row.egcontainer === 5" type="primary"
                                    @click="goslminfog(scope.row.id)">{{ scope.row.glockinfo }}</el-link>
                                <span v-else>{{ scope.row.glockinfo }}</span>
                            </template>
                        </el-table-column>
                        <!-- 盗版锁信息 -->
                        <el-table-column align="center" header-align="center" label="盗版授权锁信息" width="190"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="scope.row.plockinfo === '空'">空</span>
                                <el-link v-else-if="scope.row.egcontainer === 5" type="primary"
                                    @click="goslminfop(scope.row.id)">{{ scope.row.plockinfo }}</el-link>
                                <el-link v-else-if="scope.row.egcontainer === 1" type="primary"
                                    @click="gos4infop(scope.row.id)">{{ scope.row.plockinfo }}</el-link>
                                    <span v-else>{{ scope.row.plockinfo }}</span>
                            </template>
                        </el-table-column>
                        <!-- 机器guid -->
                        <el-table-column align="center" header-align="center" prop="guid" label="机器唯一ID" width="310"
                            show-overflow-tooltip></el-table-column>
                        <!-- 软件信息 -->
                        <el-table-column align="center" header-align="center" prop="appname" label="软件信息" width="100"
                            show-overflow-tooltip></el-table-column>
                        <!-- 异常软件模块信息 -->
                        <el-table-column align="center" header-align="center" label="异常软件模块信息" width="100"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="!scope.row.blackahxids || scope.row.blackahxids.length === 0">未发现异常</span>
                                <el-link v-else-if="scope.row.blackahxids.length === 1" type="primary"
                                    @click="">发现盗版补丁{{
                                        scope.row.blackahxids[0] }}</el-link>
                                <el-link v-else type="primary" @click="">发现多个盗版补丁</el-link>
                            </template>
                        </el-table-column>
                        <!-- 信息同步时间 -->
                        <el-table-column align="center" header-align="center" prop="lastupdatetime" label="信息同步时间"
                            width="160" show-overflow-tooltip></el-table-column>
                        <!-- 盗版判断依据 -->
                        <el-table-column align="center" header-align="center" label="盗版判定依据" width="260"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="scope.row.manual_mark_result === ''">空</span>
                                <span v-else>{{ scope.row.manual_mark_result }}</span>
                            </template>
                        </el-table-column>

                        <!-- 操作 -->
                        <el-table-column align="center" header-align="center" label="操作" width="385" fixed="right">
                            <template v-slot="scope">
                                <el-button type="primary" size="mini"
                                    @click="companyclick(scope.row.id)">公司定位</el-button>
                                <el-dialog title="公司定位" :modal="false" width="40%" top="15%"
                                    :visible.sync="companynameisvisible" @close="resetSelector">
                                    <el-radio-group v-model="companyradio" v-removeAriaHidden>
                                        <el-radio :label=true>自动定位</el-radio>
                                        <el-radio :label=false>选择定位</el-radio>
                                        <el-select v-model="value" :disabled="companyradio" placeholder="请选择">
                                            <el-option v-for="item in companyoptions" :key="item.value"
                                                :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-radio-group>
                                    <br>
                                    <br>
                                    <el-button type="primary" @click="getcompanyid">开始定位</el-button>
                                    <br>
                                    <br>
                                    <span>定位结果 : </span>
                                    <el-select v-model="selectcompanyid" style="width: 20rem;" multiple clearable
                                        collapse-tags placeholder="请选择">
                                        <el-option v-for="item in companynameoptions" :key="item.id"
                                            :label="item.companyname" :value="item.id">
                                        </el-option>
                                    </el-select>
                                    <el-button type="primary" style="margin-left: 5%;" icon="el-icon-search"
                                        @click="markcompanyid">标记</el-button>
                                    <div slot="footer" class="dialog-footer">
                                        <el-button @click="companynameisvisible = false">取消</el-button>
                                    </div>
                                </el-dialog>&nbsp;
                                <el-button type="warning" size="mini" @click="gotaskverify(scope.row)">盗版复核</el-button>
                                <el-button type="primary" size="mini"
                                    @click="Foreclick(scope.row.id)">电子取证</el-button>&nbsp;
                                <el-dialog title="电子取证" :modal="false" :visible.sync="ForensicsFormVisible">
                                    <el-form :model="form" label-width="100px">
                                        <!-- 文件类型选择 -->
                                        <el-form-item label="文件类型"
                                            :rules="[{ required: true, message: '请选择文件类型', trigger: 'change' }]">
                                            <el-select v-model="form.fileType" placeholder="请选择文件类型">
                                                <el-option label="json" value="json"></el-option>
                                                <el-option label="zip" value="zip"></el-option>
                                            </el-select>
                                        </el-form-item>

                                        <!-- 起始时间 -->
                                        <el-form-item label="起始时间">
                                            <el-date-picker v-model="form.startTime" type="datetime"
                                                placeholder="选择起始时间" :picker-options="pickerOptions" />
                                        </el-form-item>

                                        <!-- 结束时间 -->
                                        <el-form-item label="结束时间">
                                            <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间"
                                                :picker-options="pickerOptions" />
                                        </el-form-item>

                                        <!-- 下载条数 -->
                                        <el-form-item label="下载条数">
                                            <el-input-number v-model="form.downloadCount" :min="1" :max="1000" />
                                        </el-form-item>
                                    </el-form>
                                    <!-- 提交 -->
                                    <div slot="footer" class="dialog-footer">
                                        <el-button @click="ForensicsFormVisible = false">取消</el-button>
                                        <el-button type="primary" @click="Downloadforensics">确定</el-button>
                                    </div>
                                </el-dialog>
                                <el-button type="danger" @click="goexeccounter(scope.row)" size="mini">打击策略</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="sense-table-pager" v-if="total > 0">
                <el-pagination v-if="!this.isfliter" background @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="[10, 20, 30]"
                    :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
                <el-pagination v-else background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="fliterpage" :page-sizes="[10, 20, 30]" :page-size="fliterpagesize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
        <div class="all-dialog">

        </div>
    </div>
</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            loading: true, // 添加加载状态变量
            isfliter: false,
            ipinput: '',
            domaininput: '',
            lockinput: '',
            guidinput: '',
            piracyvalue: [],
            piracyoptions: [
                {
                    value: 0,
                    label: '待复核'
                },
                {
                    value: -1,
                    label: '正盗混用'
                },
                {
                    value: -2,
                    label: '纯盗版'
                },
                {
                    value: -3,
                    label: '滥用授权'
                },
                {
                    value: -4,
                    label: '回收正版锁'
                },
                {
                    value: -5,
                    label: '偷盗授权'
                },
                {
                    value: -7,
                    label: '黑名单'
                },
                {
                    value: 1,
                    label: '需人工排查'
                },
                {
                    value: 3,
                    label: '其他意见'
                },
            ],
            locktypeoptions: [
                {
                    value: 4,
                    label: '精锐4-芯片号'
                },
                {
                    value: 5,
                    label: '精锐5-外壳号'
                }
            ],
            lockvalue: '',
            companyvalue: [],

            ForensicsFormVisible: false,
            companynameisvisible: false,
            multiplecompanyVisible: false,
            isPreview: '',
            curviewdataid: '',
            companyradio: true,
            multiplecompanyradio: true,

            // 批量盗版复核
            multiplePiracyVerifyVisible: false,
            multipleVerifySelection: [],
            verifyCountValue: -1, // 默认选择所有未复核记录
            verifyCountOptions: [
                { value: 5, label: '最近5条未复核' },
                { value: 10, label: '最近10条未复核' },
                { value: 20, label: '最近20条未复核' },
                { value: 30, label: '最近30条未复核' },
                { value: -1, label: '所有未复核记录' }
            ],

            companyoptions: [{
                value: 'domain',
                label: '域名'
            }, {
                value: 'ip',
                label: 'ip'
            }, {
                value: 'wifi',
                label: 'wifi路由'
            }],
            value: '',

            companynameoptions: [],
            selectcompanyid: [],

            // 电子取证
            form: {
                fileType: '', // 文件类型
                startTime: '', // 起始时间
                endTime: '', // 结束时间
                downloadCount: 10, // 下载条数，默认 10
            },
            pickerOptions: {
                disabledDate(time) {
                    // 禁止选择未来时间
                    return time.getTime() > Date.now();
                }
            },

            exceptionListData: [],  // 展示数据
            currentPage: 1,  //当前页
            pageSize: 10,  //每页条数
            fliterpage: 1,  // 筛选时的当前页
            fliterpagesize: 10,  // 筛选时的每页条数
            total: 0,  //数据总数	
            Dialogdata: {}, // 存储app详细信息
            multipleSelection: [] // 多选数据
        }
    },
    beforeRouteLeave(to, from, next) {
        // 在用户离开当前页面之前，将消息存储到 Vuex store 中
        if (this.isfliter) {
            this.$store.commit('SET_IPINPUT0', this.ipinput);
            this.$store.commit('SET_DOMAININPUT0', this.domaininput);
            this.$store.commit('SET_LOCKINPUT0', this.lockinput);
            this.$store.commit('SET_GUIDINPUT0', this.guidinput);
            this.$store.commit('SET_PIRACYVALUE0', this.piracyvalue);
            this.$store.commit('SET_LOCKVALUE0', this.lockvalue);
            this.$store.commit('SET_COMPANYVALUE0', this.companyvalue);
        }
        next();
    },
    methods: {
        // 多条公司定位
        multiplecompanyclick() {
            if (this.multipleSelection.length > 0) {
                this.multiplecompanyVisible = true;
            } else {
                this.$message({
                    message: '请选择要定位的公司',
                    type: 'warning'
                })
            }
        },

        // 多条公司获取
        async multiplegetcompanyid() {
            this.$message.success('定位已发送');
            try {
                const responses = await Promise.all(
                    this.multipleSelection.map((item) => {
                        return new Promise((resolve) => {
                            apiClient.post('/lic/getcompanyidbyviewdataid', { 'viewdataid': item.id, }).then(
                                (res) => {
                                    if (res.data.code == 0) {
                                        if (res.data.data != '') {
                                            item.selectcompanyid = res.data.data;
                                        } else {
                                            this.$message({
                                                message: item.hostname + "-" + item.appname + "未查询到,请手动定位",
                                                type: 'info'
                                            });
                                        }
                                    } else {
                                        this.$message({
                                            message: item.hostname + "-" + item.appname + "定位失败:" + res.data.msg,
                                            type: 'warning'
                                        });
                                    }
                                    resolve(item); // 继续处理 Promise
                                }
                            )
                        })
                    })
                );
                this.$message.success('定位完成');
            } catch (error) {
                this.$message({
                    message: '定位失败',
                    type: 'warning'
                });
            }
        },

        // 多条公司定位标记
        async multiplecompanymark() {
            this.$message.success('标记已发送');
            try {
                const responses = await Promise.all(
                    this.multipleSelection.map((item) => {
                        if (item.selectcompanyid.length > 0) {
                            return new Promise((resolve) => {
                                apiClient.post('/lic/markcompanyidbyviewdataid', {
                                    "viewdataid": item.id,
                                    "companyids": item.selectcompanyid
                                }).catch(error => {
                                    this.$message({
                                        message: item.hostname + "-" + item.appname + "标记失败:" + error,
                                        type: 'warning'
                                    });
                                })
                                    .then(response => {
                                        if (response.data.code != 0) {
                                            this.$message({
                                                message: item.hostname + "-" + item.appname + "标记失败:" + response.data.msg,
                                                type: 'warning'
                                            })
                                        }
                                    })
                                resolve(item); // 继续处理 Promise
                            })
                        }
                    })
                );
                this.$message.success('标记完成');
                this.multiplecompanyVisible = false;
                if (!this.isfliter) {
                    this.getadddata(this.currentPage, this.pageSize);
                } else {
                    this.getviewdatabyfliter(this.fliterpage, this.fliterpagesize);
                }
            } catch (error) {
                this.$message({
                    message: '标记失败',
                    type: 'warning'
                });
            }
        },
        // 多条公司定位窗口关闭
        multiplecompanyclose() {
            this.multipleSelection.map((item) => {
                item.selectcompanyid = [];
            })
        },

        // 批量盗版复核
        multiplePiracyVerifyClick() {
            if (this.multipleSelection.length > 0) {
                this.multiplePiracyVerifyVisible = true;
                this.multipleVerifySelection = this.multipleSelection.map(item => {
                    return {
                        ...item,
                        verifyStatus: '',
                        loading: false
                    };
                });
            } else {
                this.$message({
                    message: '请选择要复核的记录',
                    type: 'warning'
                });
            }
        },

        // 批量盗版复核窗口关闭
        multiplePiracyVerifyClose() {
            this.multipleVerifySelection = [];
            this.verifyCountValue = -1; // 重置为默认选择所有未复核记录
        },

        // 开始批量复核
        async startMultipleVerify() {
            if (this.multipleVerifySelection.length === 0) {
                this.$message.warning('没有选择要复核的记录');
                return;
            }

            this.$message.success('批量复核已发送');
            
            try {

                const responses = await Promise.all(
                    this.multipleVerifySelection.map((item) => {
                        return new Promise((resolve) => {
                            // 设置加载状态
                            this.$set(item, 'loading', true);
                            apiClient.post('/lic/reviewviewdata', { 'viewdataid': item.id, 'verifycount': this.verifyCountValue }).then(
                                (res) => {
                                    if (res.data.code === 0) {
                                        this.$set(item, 'verifyStatus', 'success');
                                    } else {
                                        this.$set(item, 'verifyStatus', 'error');
                                        this.$message.error(`${item.hostname} 复核失败: ${res.data.msg}`);
                                    }
                                    // 结束加载状态
                                    this.$set(item, 'loading', false);
                                    resolve(item);
                                }
                            ).catch((error) => {
                                this.$set(item, 'verifyStatus', 'error');
                                this.$message.error(`${item.hostname} 复核失败: ${error}`);
                                this.$set(item, 'loading', false);
                                resolve(item);
                            });
                        });
                    })
                );
                
                this.$message.success('批量复核完成');
                // 更新表格数据
                if (!this.isfliter) {
                    this.getadddata(this.currentPage, this.pageSize);
                } else {
                    this.getviewdatabyfliter(this.fliterpage, this.fliterpagesize);
                }
            } catch (error) {
                this.$message.error('批量复核失败');
            }
        },

        // 单独复核指定记录
        gotoSingleVerify(row) {
            this.gotaskverify(row);
        },

        getMap() {
            this.$router.push('/mapinfo');
        },
        Foreclick(viewdataid) {
            this.ForensicsFormVisible = true;
            this.curviewdataid = viewdataid;
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        // 公司定位
        companyclick(viewdataid) {
            this.companynameisvisible = true;
            this.curviewdataid = viewdataid;
            // 获取所有公司名称
            apiClient.get('/getallcompanynames').then(response => {
                this.companynameoptions = response.data.data;
            }).catch(error => {
                this.$message({
                    message: error,
                    type: 'warning'
                });
            });
        },
        // 自动获取公司id
        getcompanyid() {
            if (this.companyradio) {
                apiClient.post('/lic/getcompanyidbyviewdataid', {
                    "viewdataid": this.curviewdataid
                }).catch(error => {
                    this.$message({
                        message: error,
                        type: 'warning'
                    });
                }).then(response => {
                    if (response.data.code == 0) {
                        if (response.data.data != '') {
                            this.selectcompanyid = response.data.data;
                            this.$message({
                                message: "定位成功",
                                type: 'success',
                                duration: 1000
                            });
                        } else {
                            this.$message({
                                message: "未查询到,请手动定位",
                                type: 'info'
                            });
                        }
                    } else {
                        this.$message({
                            message: "定位失败:" + response.data.msg,
                            type: 'warning'
                        });
                    }
                });
            } else {
                if (this.value == '') {
                    this.$message.warning('请选择定位方式');
                    return;
                }
                // 域名定位
                if (this.value == 'domain') {
                    apiClient.post('/lic/getcompanyidwithdomain', {
                        "viewdataid": this.curviewdataid
                    }).catch(error => {
                        this.$message({
                            message: error,
                            type: 'warning'
                        });
                    }).then(response => {
                        if (response.data.code == 0) {
                            if (response.data.data != '') {
                                this.selectcompanyid = response.data.data;
                                this.$message({
                                    message: "定位成功",
                                    type: 'success',
                                    duration: 1000
                                });
                            } else {
                                this.$message({
                                    message: "未查询到,请手动定位",
                                    type: 'info'
                                });
                            }
                        } else {
                            this.$message({
                                message: "定位失败:" + response.data.msg,
                                type: 'warning'
                            });
                        }
                    });
                } else if (this.value == 'ip') {
                    // ip定位
                    apiClient.post('/lic/getcompanyidwithip', {
                        "viewdataid": this.curviewdataid
                    }).catch(error => {
                        this.$message({
                            message: error,
                            type: 'warning'
                        });
                    }).then(response => {
                        if (response.data.code == 0) {
                            if (response.data.data != '') {
                                this.selectcompanyid = response.data.data;
                                this.$message({
                                    message: "定位成功",
                                    type: 'success',
                                    duration: 1000
                                });
                            } else {
                                this.$message({
                                    message: "未查询到,请手动定位",
                                    type: 'info'
                                });
                            }
                        } else {
                            this.$message({
                                message: "定位失败:" + response.data.msg,
                                type: 'warning'
                            });
                        }
                    });
                } else if (this.value == 'wifi') {
                    // wifi定位
                    apiClient.post('/lic/getcompanyidwithwifi', {
                        "viewdataid": this.curviewdataid
                    }).catch(error => {
                        this.$message({
                            message: error,
                            type: 'warning'
                        });
                    }).then(response => {
                        if (response.data.code == 0) {
                            if (response.data.data != '') {
                                this.selectcompanyid = response.data.data;
                                this.$message({
                                    message: "定位成功",
                                    type: 'success',
                                    duration: 1000
                                });
                            } else {
                                this.$message({
                                    message: "未查询到,请手动定位",
                                    type: 'info'
                                });
                            }
                        } else {
                            this.$message({
                                message: "定位失败:" + response.data.msg,
                                type: 'warning'
                            });
                        }
                    });
                }
            }
        },
        // dialog关闭时清空数据
        resetSelector() {
            this.selectcompanyid = '';
            this.value = '';
        },
        // 标记某viewdata公司id
        markcompanyid() {
            if (this.selectcompanyid.length == 0) {
                this.$message.warning('请选择公司名称');
                return;
            }
            apiClient.post('/lic/markcompanyidbyviewdataid', {
                "viewdataid": this.curviewdataid,
                "companyids": this.selectcompanyid
            }).catch(error => {
                this.$message({
                    message: error,
                    type: 'warning'
                });
            }).then(response => {
                if (response.data.code == 0) {
                    this.$message({
                        message: "标记成功",
                        type: 'success',
                        duration: 1000
                    });
                    if (!this.isfliter) {
                        this.getadddata(this.currentPage, this.pageSize);
                    } else {
                        this.getviewdatabyfliter(this.fliterpage, this.fliterpagesize);
                    }
                } else {
                    this.$message({
                        message: "标记失败:" + response.data.msg,
                        type: 'warning'
                    });
                }
            });
        },

        // 跳转task复核
        gotaskverify(row) {
            this.$store.commit('SET_VIEWDATAID', row.id);
            this.$store.commit('SET_Task_HOSTNAME', row.hostname);
            this.$store.commit('SET_Task_GUID', row.guid);
            this.$store.commit('SET_Task_COMPANYNAME', row.companyname);
            this.$store.commit('SET_Task_APPNAME', row.appname);
            this.$router.push('/taskverify');
        },

        // 电子取证
        Downloadforensics() {
            if (this.form.fileType == '') {
                this.$message.error('请选择文件类型');
                return;
            }
            let url = '/lic/getforensics';
            var param = {
                "viewdataid": this.curviewdataid,
                "filetype": this.form.fileType,
                "starttime": this.form.startTime,
                "endtime": this.form.endTime,
                "limit": this.form.downloadCount
            }
            // 发送POST请求
            apiClient.post(url, param, {
                responseType: 'blob' // 处理二进制文件
            }).catch(error => {
                this.$message.error("下载失败:" + error.message);
                return
            }).then(response => {
                // 判断返回的文件类型
                const contentType = response.headers['content-type'];
                const contentDisposition = response.headers['content-disposition'];
                let filename = 'download'; // 默认文件名

                // 解析文件名
                if (contentDisposition && contentDisposition.indexOf('filename=') !== -1) {
                    filename = contentDisposition.split('filename=')[1].replace(/"/g, '');
                } else {
                    // 如果没有文件名，则使用默认的 filetype 来命名
                    if (this.form.fileType == 'json') {
                        filename = 'data.json';
                    } else if (this.form.fileType == 'zip') {
                        filename = 'data.zip';
                    }
                }

                // 创建Blob并触发下载
                const blob = new Blob([response.data], { type: contentType });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
            });

        },

        // s4正版锁信息
        gos4infog(value) {
            this.$store.commit('SET_VIEWDATAID', value);
            this.$store.commit('SET_S4ISPIRACY', true);
            this.$router.push('/s4info');
        },

        // s4盗版锁信息
        gos4infop(value) {
            this.$store.commit('SET_VIEWDATAID', value);
            this.$store.commit('SET_S4ISPIRACY', false);
            this.$router.push('/s4info');
        },

        // slm正版锁信息
        goslminfog(value) {
            this.$store.commit('SET_VIEWDATAID', value);
            this.$store.commit('SET_SLmISPIRACY', true);
            this.$router.push('/slminfo');
        },

        // slm盗版锁信息
        goslminfop(value) {
            this.$store.commit('SET_VIEWDATAID', value);
            this.$store.commit('SET_SLmISPIRACY', false);
            this.$router.push('/slminfo');
        },

        // 打击策略
        goexeccounter(row) {
            this.$store.commit('SET_VIEWDATAID', row.id);
            this.$store.commit('SET_EXEC_HOSTNAME', row.hostname);
            this.$store.commit('SET_EXEC_COMPANYNAME', row.companyname);
            this.$store.commit('SET_EXEC_APPID', row.appid);
            this.$store.commit('SET_EXEC_GUID', row.guid);
            this.$router.push('/execcounter');
        },

        //切换每页显示条数
        handleSizeChange(val) {
            if (!this.isfliter) {
                this.pageSize = val;
                this.$store.commit('SET_PAGE_SIZE', val);
                this.getadddata(this.currentPage, val);
            } else {
                this.fliterpagesize = val;
                this.$store.commit('SET_FLITER_SIZE0', val);
                this.getviewdatabyfliter(this.fliterpage, val);
            }
        },
        //切换当前页
        handleCurrentChange(val) {
            if (!this.isfliter) {
                this.currentPage = val;
                this.$store.commit('SET_CUR_PAGE', val);
                this.getadddata(val, this.pageSize);
            } else {
                this.fliterpage = val;
                this.$store.commit('SET_FLITER_PAGE0', val);
                this.getviewdatabyfliter(val, this.fliterpagesize);
            }
        },
        getadddata(page, size) {
            this.loading = true; // 在请求前设置加载状态为true
            let url = '/getviewdata';
            var param = {
                "page": page,
                "limit": size,
                "type": 0
            }
            apiClient.post(url, param).catch(error => {
                this.$message.error("server:" + error.message);
                this.loading = false; // 确保在请求失败时也将加载状态设为false
                return
            })
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.res == null) {
                            this.exceptionListData = [];
                            this.total = 0;
                        } else {
                            this.exceptionListData = res.data.res;
                            this.exceptionListData.forEach(element => {
                                element.iplocationhistory = element.iplocationhistory == null ? "未知" : element.iplocationhistory.join(',');
                            })
                            this.total = res.data.totalcount;
                        }
                    } else {
                        this.$message({
                            message: "error:" + res.data.msg,
                            type: 'warning'
                        });
                    }
                    this.loading = false; // 在请求完成后设置加载状态为false
                }
                )
        },

        // 过滤查询
        getviewdatabyfliter(page, size) {
            this.loading = true; // 在请求前设置加载状态为true
            let url = '/getviewdatabyfilter';
            let param = {
                "type": 1,
                "page": page,
                "limit": size,
            }
            this.fliterpage = page;
            this.$store.commit('SET_FLITER_PAGE0', page);
            this.fliterpagesize = size;
            if (this.ipinput != '') {
                param['ip'] = this.ipinput;
            }
            if (this.domaininput != '') {
                param['domain'] = this.domaininput;
            }
            if (this.piracyvalue.length != 0) {
                param['piracytype'] = this.piracyvalue;
            }
            if (this.lockvalue != '') {
                param['locktype'] = this.lockvalue;
                if (this.lockinput == '') {
                    this.$message.warning('请输入锁号');
                    this.loading = false; // 确保在验证失败时也将加载状态设为false
                    return;
                }
                param['locknum'] = this.lockinput;
            }
            if (this.companyvalue.length != 0) {
                param['companyid'] = this.companyvalue;
            }
            if (this.guidinput != '') {
                param['guid'] = this.guidinput;
            }
            apiClient.post(url, param).catch(error => {
                this.$message.error("查询失败:" + error.message);
                this.loading = false; // 确保在请求失败时也将加载状态设为false
                return
            })
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.res == null) {
                            this.exceptionListData = [];
                            this.total = 0;
                        } else {
                            this.exceptionListData = res.data.res;
                            this.exceptionListData.forEach(element => {
                                element.iplocationhistory = element.iplocationhistory == null ? "未知" : element.iplocationhistory.join(',');
                            })
                            this.total = res.data.totalcount;
                        }
                        this.isfliter = true;
                        this.$store.commit('SET_VIEWDATA1ISFLITER', true);
                        this.$store.commit('SET_IPINPUT0', this.ipinput);
                        this.$store.commit('SET_DOMAININPUT0', this.domaininput);
                        this.$store.commit('SET_LOCKINPUT0', this.lockinput);
                        this.$store.commit('SET_GUIDINPUT0', this.guidinput);
                        this.$store.commit('SET_PIRACYVALUE0', this.piracyvalue);
                        this.$store.commit('SET_LOCKVALUE0', this.lockvalue);
                        this.$store.commit('SET_COMPANYVALUE0', this.companyvalue);
                        this.$message({
                            message: "查询成功",
                            type: 'success',
                            duration: 1000
                        })
                    }
                    else {
                        this.$message({
                            message: "error:" + res.data.msg,
                            type: 'warning'
                        });
                    }
                    this.loading = false; // 在请求完成后设置加载状态为false
                })
        },

        // 重置
        resetfliter() {
            this.ipinput = '';
            this.domaininput = '';
            this.lockinput = '';
            this.guidinput = '';
            this.piracyvalue = [];
            this.lockvalue = '';
            this.companyvalue = [];
        },
    },
    mounted() {
        this.currentPage = this.$store.state.CurPage.curPage;
        this.pageSize = this.$store.state.CurPage.pageSize;
        this.fliterpage = this.$store.state.CurPage.fliterPage0;
        this.fliterpagesize = this.$store.state.CurPage.fliterSize0;
        this.isfliter = this.$store.state.CurPage.viewdata1isfliter;
        if (!this.isfliter) {
            this.getadddata(this.currentPage, this.pageSize);
        } else {
            this.ipinput = this.$store.state.CurPage.ipinput0;
            this.domaininput = this.$store.state.CurPage.domaininput0;
            this.lockinput = this.$store.state.CurPage.lockinput0;
            this.guidinput = this.$store.state.CurPage.guidinput0;
            this.piracyvalue = this.$store.state.CurPage.piracyvalue0;
            this.lockvalue = this.$store.state.CurPage.lockvalue0;
            this.companyvalue = this.$store.state.CurPage.companyvalue0;
            this.getviewdatabyfliter(this.fliterpage, this.fliterpagesize);
        }
        // 获取所有公司名称
        apiClient.get('/getallcompanynames').then(response => {
            this.companynameoptions = response.data.data;
        }).catch(error => {
            this.$message({
                message: error,
                type: 'warning'
            });
        });
    },
}
</script>

<style scoped lang="scss">
/* 加载动画样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
}

.loading-container i {
    font-size: 40px;
    color: #409EFF;
    margin-bottom: 15px;
}

.loading-container p {
    font-size: 16px;
    color: #606266;
}
</style>