<template>
    <div>
        <div class="sense-main">
            <el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
            <br>
            <br>
            <span>&nbsp;&nbsp; 正盗版: &nbsp;</span>
            <el-select v-model="slmispiracy" placeholder="请选择" clearable size="small">
                <el-option v-for="item in piracytype" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>&nbsp;&nbsp;&nbsp;
            <el-button type="primary" size="small" @click="getSlmInfo" round>查询数据</el-button>
            <br>
            <br>
            <el-table :data="paginatedData" border style="width: 100%">
                <el-table-column prop="slminfo.container" align="center" header-align="center" label="容器类型">
                </el-table-column>
                <el-table-column prop="slminfo.developeridfromdevice" align="center" header-align="center"
                    label="开发商id(锁读取)">
                </el-table-column>
                <el-table-column prop="slminfo.developeridfromlib" align="center" header-align="center"
                    label="开发商id(库读取)">
                </el-table-column>
                <el-table-column prop="slminfo.licid" align="center" header-align="center" label="许可id">
                </el-table-column>
                <el-table-column align="center" header-align="center" label="外壳号">
                    <template slot-scope="scope">
                        <span v-if="scope.row.slminfo.e5casesn === ''">空</span>
                        <span v-else>{{ scope.row.slminfo.e5casesn }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="slminfo.e5chipsn" align="center" header-align="center" label="芯片号" width="285">
                </el-table-column>
                <el-table-column align="center" header-align="center" prop="slminfo.detectid" label="detectid">
                </el-table-column>
                <el-table-column align="center" header-align="center" label="软锁序列号">
                    <template slot-scope="scope">
                        <span v-if="scope.row.slminfo.slsn === ''">空</span>
                        <span v-else>{{ scope.row.slminfo.slsn }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" header-align="center" label="account">
                    <template slot-scope="scope">
                        <span v-if="scope.row.slminfo.cl_account === ''">空</span>
                        <span v-else>{{ scope.row.slminfo.cl_account }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <br>
            <el-pagination background @current-change="handlePageChange" :current-page="currentPage"
                :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="pagination" />
        </div>

    </div>

</template>

<script>
import apiClient from '../api/axios'
export default {
    data() {
        return {
            slmdatas: [], // 原始数据
            currentPage: 1, // 当前页码
            pageSize: 10, // 每页条数
            total: 0,// 总条数
            viewdataid: '',
            slmispiracy: false,
            datasources: [
                {
                    value: 1,
                    label: '在线检测'
                },
                {
                    value: 2,
                    label: '离线检测'
                },
            ],
            datasource: '',
            piracytype: [
                {
                    value: true,
                    label: '正版'
                },
                {
                    value: false,
                    label: '盗版'
                },
            ],
        }
    },
    // 获取viewdataid
    mounted() {
        this.slmispiracy = this.$store.state.Slminfo.slmispiracy;
        this.viewdataid = this.$store.state.Viewdataid.viewdataid;
        this.getSlmInfo();
    },
    computed: {
        // 计算当前页的数据
        paginatedData() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.slmdatas.slice(start, end);
        }
    },
    methods: {
        goback() {
            this.$router.go(-1);
        },
        // 页码切换时触发
        handlePageChange(page) {
            this.currentPage = page;
        },
        // 获取Slminfo
        getSlmInfo() {
            let url = '/getpcslminfos';
            var params = {
                "viewdataid": this.viewdataid,
                "ispiracy": this.slmispiracy
            }
            apiClient.post(url, params).catch(err => {
                this.$message.error("server:" + err.message);
                return
            }).then(res => {
                if (res.data.code == 0) {
                    this.slmdatas = res.data.data;
                    this.total = res.data.data.length;
                    this.$message({
                        message: '获取数据成功',
                        type: 'success'
                    });
                } else {
                    if (res.data.msg != null) {
                        this.$message({
                            message: res.data.msg,
                            type: 'warning'
                        });
                        return
                    } else {
                        this.$message({
                            message: "获取数据出错",
                            type: 'warning'
                        });
                    }
                }
            })
        }
    },
}
</script>
