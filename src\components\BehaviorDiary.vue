<template>
	<div>
		<div class="sense-main">
			<div class="sense-breadcrumb">
				<el-breadcrumb separator-class="el-icon-arrow-right">
				    <el-breadcrumb-item :to="{ path: '/piracyConfrontation' }">{{machineId}}</el-breadcrumb-item>
				    <el-breadcrumb-item>行为日志</el-breadcrumb-item>
				</el-breadcrumb>
			</div>			
			<div class="sense-table-list sense-table-ellipsis">
				<el-table :data="diaryListData" border>
					<el-table-column label="消息类型" min-width="13%">
						<template slot-scope="scope">
							<span v-if="scope.row.msgType == 0" class="sense-mestype sense-mestype-0">未知</span>
							<span v-if="scope.row.msgType == 4" class="sense-mestype sense-mestype-1">盗版软件</span>
							<span v-if="scope.row.msgType == 3" class="sense-mestype sense-mestype-2">疑似盗版</span>
							<span v-if="scope.row.msgType == 2" class="sense-mestype sense-mestype-3">可疑行为</span>									
							<span v-if="scope.row.msgType == 5" class="sense-mestype sense-mestype-4">异常消息</span>
							<span v-if="scope.row.msgType == 1" class="sense-mestype sense-mestype-5">正常</span>
						</template>
					</el-table-column>														
					<el-table-column prop="exeName" label="软件名称" min-width="20%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="desc" label="行为内容" min-width="21%" show-overflow-tooltip></el-table-column>						
					<el-table-column :formatter="areaFormatter" label="省/市" min-width="16%" show-overflow-tooltip></el-table-column>
					<el-table-column prop="timestamp" label="终端时间" min-width="15%"></el-table-column>
					<el-table-column prop="reportTime" label="上报时间" min-width="15%"></el-table-column>		
				</el-table>
			</div>
			<div class="sense-table-pager" v-if="total>0">
				<el-pagination
					background
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="currentPage"
					:page-sizes="[10,20,30]"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total">
				</el-pagination>
			</div>
		</div>
		<div class="all-dialog">
			
		</div>
	</div>
</template>

<script>

	export default {
		data() {
			return {
				isPreview: '',
				previewObj: {
					diaryListData: [
						{'msgType': '0', 'exeName': 'word.exe', 'province': '黑龙江', 'city': '大庆', 'desc': '检测到word.exe的反黑启动失败，返回:0x102', 'timestamp': '2019-01-31 21:50:00', 'reportTime': '2019-01-31 21:50:00'},
						{'msgType': '2', 'exeName': 'photoshop.exe', 'province': '北京', 'city': '北京', 'desc': '检测到photoshop.exe的内存中包含恶意特征ID:0x18', 'timestamp': '2019-01-28 12:50:35', 'reportTime': '2019-01-28 12:50:35'},
						{'msgType': '3', 'exeName': 'excel.exe', 'province': '浙江', 'city': '杭州', 'desc': '种植木马', 'timestamp': '2019-01-20 20:50:00', 'reportTime': '2019-01-20 20:50:00'},
						{'msgType': '4', 'exeName': 'word.exe', 'province': '河南', 'city': '郑州', 'desc': '检测到word.exe的反黑启动失败，返回:0x105', 'timestamp': '2019-01-16 08:50:00', 'reportTime': '2019-01-16 08:50:00'},
						{'msgType': '5', 'exeName': 'Foxmail.exe', 'province': '陕西', 'city': '西安', 'desc': '检测到Foxmail.exe的EnumDevice函数被挂钩', 'timestamp': '2019-01-15 10:50:07', 'reportTime': '2019-01-15 10:50:07'},
						{'msgType': '1', 'exeName': 'word.exe', 'province': '黑龙江', 'city': '大庆', 'desc': '种植木马', 'timestamp': '2019-01-13 21:50:00', 'reportTime': '2019-01-13 21:50:00'},
						{'msgType': '2', 'exeName': 'photoshop.exe', 'province': '北京', 'city': '北京', 'desc': '检测到photoshop.exe的内存中包含恶意特征ID:0x18', 'timestamp': '2019-01-11 12:50:35', 'reportTime': '2019-01-11 12:50:35'},
						{'msgType': '1', 'exeName': 'excel.exe', 'province': '浙江', 'city': '杭州', 'desc': '检测到excel.exe的反黑启动失败，返回:0x108', 'timestamp': '2019-01-08 20:50:00', 'reportTime': '2019-01-08 20:50:00'},
						{'msgType': '3', 'exeName': 'word.exe', 'province': '河南', 'city': '郑州', 'desc': '种植木马', 'timestamp': '2019-01-05 08:50:00', 'reportTime': '2019-01-05 08:50:00'},
						{'msgType': '5', 'exeName': 'Foxmail.exe', 'province': '陕西', 'city': '西安', 'desc': '检测到Foxmail.exe的EnumDevice函数被挂钩', 'timestamp': '2019-01-02 10:50:07', 'reportTime': '2019-01-02 10:50:07'},
					],
					total: 10
				},
				machineId: '',
				id: '',
				diaryListData: [],  //行为日志列表数组
				currentPage: 1,  //当前页
				pageSize: 10,  //每页条数
				total: 0,  //数据总数	
			}
		},
		methods: {
			getDiaryList() {	
				if (this.isPreview == 1) {
					this.diaryListData = this.previewObj.diaryListData;
					this.total = this.previewObj.total;
				} else {
					
				};					
			},	
			//格式化省市
			areaFormatter(row, index) {
				return row.province + '-' + row.city;
			},		
			//切换每页显示条数
			handleSizeChange(val) {
				this.pageSize = val;
				this.getDiaryList();
			},
			//切换当前页
			handleCurrentChange(val) {
				this.currentPage = val;
				this.getDiaryList();
			},
		},
		mounted() {
			this.isPreview = sessionStorage.getItem('isPreview');
			this.machineId = this.$route.query.machineId;
			this.id = this.$route.query.id;
			this.getDiaryList();
		},
	}
</script>

<style scoped lang="scss">	

</style>