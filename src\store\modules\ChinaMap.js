
const state = {
  geoCoordMap: {
    '香港特别行政区': [114.08, 22.2], 
    '澳门特别行政区': [113.33, 22.13], 
    '台北': [121.5, 25.03], 
    '基隆': [121.73, 25.13], 
    '台中': [120.67, 24.15], 
    '台南': [120.2, 23.0], 
    '宜兰县': [121.75, 24.77], 
    '桃园县': [121.3, 24.97], 
    '苗栗县': [120.8, 24.53], 
    '台中县': [120.72, 24.25], 
    '彰化县': [120.53, 24.08], 
    '南投县': [120.67, 23.92], 
    '云林县': [120.53, 23.72], 
    '台南县': [120.32, 23.32], 
    '高雄县': [120.37, 22.63], 
    '屏东县': [120.48, 22.67], 
    '台东县': [121.15, 22.75], 
    '花莲县': [121.6, 23.98], 
    '澎湖县': [119.58, 23.58], 
    '石家庄': [114.52, 38.05], 
    '唐山': [118.2, 39.63], 
    '秦皇岛': [119.6, 39.93], 
    // 河北省
    '邯郸': [114.48, 36.62],
    '邢台': [114.48, 37.07],
    '保定': [115.47, 38.87],
    '张家口': [114.88, 40.82],
    '承德': [117.93, 40.97],
    '沧州': [116.83, 38.3],
    '廊坊': [116.7, 39.52],
    '衡水': [115.68, 37.73],

    // 山西省
    '太原': [112.55, 37.87],
    '大同': [113.3, 40.08],
    '阳泉': [113.57, 37.85],
    '长治': [113.12, 36.2],
    '晋城': [112.83, 35.5],
    '朔州': [112.43, 39.33],
    '晋中': [112.75, 37.68],
    '运城': [110.98, 35.02],
    '忻州': [112.73, 38.42],
    '临汾': [111.52, 36.08],
    '吕梁': [111.13, 37.52],

    // 内蒙古自治区
    '呼和浩特': [111.73, 40.83],
    '包头': [109.83, 40.65],
    '乌海': [106.82, 39.67],
    '赤峰': [118.92, 42.27],
    '通辽': [122.27, 43.62],
    '鄂尔多斯': [109.8, 39.62],
    '呼伦贝尔': [119.77, 49.22],
    '巴彦淖尔': [107.42, 40.75],
    '乌兰察布': [113.12, 40.98],
    '兴安盟': [122.05, 46.08],
    '锡林郭勒盟': [116.07, 43.95],
    '阿拉善盟': [105.67, 38.83],

    // 辽宁省
    '沈阳': [123.43, 41.8],
    '大连': [121.62, 38.92],
    '鞍山': [122.98, 41.1],
    '抚顺': [123.98, 41.88],
    '本溪': [123.77, 41.3],
    '丹东': [124.38, 40.13],
    '锦州': [121.13, 41.1],
    '营口': [122.23, 40.67],
    '阜新': [121.67, 42.02],
    '辽阳': [123.17, 41.27],
    '盘锦': [122.07, 41.12],
    '铁岭': [123.83, 42.28],
    '朝阳': [120.45, 41.57],
    '葫芦岛': [120.83, 40.72],

    // 吉林省
    '长春': [125.32, 43.9],
    '吉林': [126.55, 43.83],
    '四平': [124.35, 43.17],
    '辽源': [125.13, 42.88],
    '通化': [125.93, 41.73],
    '白山': [126.42, 41.93],
    '松原': [124.82, 45.13],
    '白城': [122.83, 45.62],
    '延边朝鲜族自治州': [129.5, 42.88],

    // 黑龙江省
    '哈尔滨': [126.53, 45.8],
    '齐齐哈尔': [123.95, 47.33],
    '鸡西': [130.97, 45.3],
    '鹤岗': [130.27, 47.33],
    '双鸭山': [131.15, 46.63],
    '大庆': [125.03, 46.58],
    '伊春': [128.9, 47.73],
    '佳木斯': [130.37, 46.82],
    '七台河': [130.95, 45.78],
    '牡丹江': [129.6, 44.58],
    '黑河': [127.48, 50.25],
    '绥化': [126.98, 46.63],
    '大兴安岭地区': [124.12, 50.42],

    // 江苏省
    '南京': [118.78, 32.07],
    '无锡': [120.3, 31.57],
    '徐州': [117.18, 34.27],
    '常州': [119.95, 31.78],
    '苏州': [120.58, 31.3],
    '南通': [120.88, 31.98],
    '连云港': [119.22, 34.6],
    '淮安': [119.02, 33.62],
    '盐城': [120.15, 33.35],
    '扬州': [119.4, 32.4],
    '镇江': [119.45, 32.2],
    '泰州': [119.92, 32.45],
    '宿迁': [118.28, 33.97],

    // 浙江省
    '杭州': [120.15, 30.28],
    '宁波': [121.55, 29.88],
    '温州': [120.7, 28.0],
    '嘉兴': [120.75, 30.75],
    '湖州': [120.08, 30.9],
    '绍兴': [120.57, 30.0],
    '金华': [119.65, 29.08],
    '衢州': [118.87, 28.93],
    '舟山': [122.2, 30.0],
    '台州': [121.43, 28.68],
    '丽水': [119.92, 28.45],

    // 安徽省
    '合肥': [117.25, 31.83],
    '芜湖': [118.38, 31.33],
    '蚌埠': [117.38, 32.92],
    '淮南': [117.0, 32.63],
    '马鞍山': [118.5, 31.7],
    '淮北': [116.8, 33.95],
    '铜陵': [117.82, 30.93],
    '安庆': [117.05, 30.53],
    '黄山': [118.33, 29.72],
    '滁州': [118.32, 32.3],
    '阜阳': [115.82, 32.9],
    '宿州': [116.98, 33.63],
    '巢湖': [117.87, 31.6],
    '六安': [116.5, 31.77],
    '亳州': [115.78, 33.85],
    '池州': [117.48, 30.67],
    '宣城': [118.75, 30.95],

    // 福建省
    '福州': [119.3, 26.08],
    '厦门': [118.08, 24.48],
    '莆田': [119.0, 25.43],
    '三明': [117.62, 26.27],
    '泉州': [118.67, 24.88],
    '漳州': [117.65, 24.52],
    '南平': [118.17, 26.65],
    '龙岩': [117.03, 25.1],
    '宁德': [119.52, 26.67],

    // 江西省
    '南昌': [115.85, 28.68],
    '景德镇': [117.17, 29.27],
    '萍乡': [113.85, 27.63],
    '九江': [116.0, 29.7],
    '新余': [114.92, 27.82],
    '鹰潭': [117.07, 28.27],
    '赣州': [114.93, 25.83],
    '吉安': [114.98, 27.12],
    '宜春': [114.38, 27.8],
    '抚州': [116.35, 28.0],
    '上饶': [117.97, 28.45],

    // 山东省
    '济南': [116.98, 36.67],
    '青岛': [120.38, 36.07],
    '淄博': [118.05, 36.82],
    '枣庄': [117.32, 34.82],
    '东营': [118.67, 37.43],
    '烟台': [121.43, 37.45],
    '潍坊': [119.15, 36.7],
    '济宁': [116.58, 35.42],
    '泰安': [117.08, 36.2],
    '威海': [122.12, 37.52],
    '日照': [119.52, 35.42],
    '莱芜': [117.67, 36.22],
    '临沂': [118.35, 35.05],
    '德州': [116.3, 37.45],
    '聊城': [115.98, 36.45],
    '滨州': [117.97, 37.38],

    // 河南省
    '郑州': [113.62, 34.75],
    '开封': [114.3, 34.8],
    '洛阳': [112.45, 34.62],
    '平顶山': [113.18, 33.77],
    '安阳': [114.38, 36.1],
    '鹤壁': [114.28, 35.75],
    '新乡': [113.9, 35.3],
    '焦作': [113.25, 35.22],
    '济源': [112.58, 35.07],
    '濮阳': [115.03, 35.77],
    '许昌': [113.85, 34.03],
    '漯河': [114.02, 33.58],
    '三门峡': [111.2, 34.78],
    '南阳': [112.52, 33.0],
    '商丘': [115.65, 34.45],
    '信阳': [114.07, 32.13],
    '周口': [114.65, 33.62],
    '驻马店': [114.02, 32.98],
    '神农架林区': [110.67, 31.75],

    // 湖北省
    '武汉': [114.3, 30.6],
    '黄石': [115.03, 30.2],
    '十堰': [110.78, 32.65],
    '宜昌': [111.28, 30.7],
    '鄂州': [114.88, 30.4],
    '荆门': [112.2, 31.03],
    '孝感': [113.92, 30.93],
    '荆州': [112.23, 30.33],
    '黄冈': [114.87, 30.45],
    '咸宁': [114.32, 29.85],
    '随州': [113.37, 31.72],
    '恩施土家族苗族自治州': [109.47, 30.3],
    '仙桃': [113.45, 30.37],
    '潜江': [112.88, 30.42],
    '天门': [113.17, 30.67],

    // 湖南省
    '长沙': [112.93, 28.23],
    '株洲': [113.13, 27.83],
    '湘潭': [112.93, 27.83],
    '衡阳': [112.57, 26.9],
    '邵阳': [111.47, 27.25],
    '岳阳': [113.12, 29.37],
    '常德': [111.68, 29.05],
    '张家界': [110.47, 29.13],
    '益阳': [112.32, 28.6],
    '郴州': [113.02, 25.78],
    '永州': [111.62, 26.43],
    '怀化': [110.0, 27.57],
    '娄底': [112.0, 27.73],
    '湘西土家族苗族自治州': [109.73, 28.32],

    // 广东省
    '广州': [113.5107, 23.2196],
    '韶关': [113.6, 24.82],
    '深圳': [114.05, 22.55],
    '珠海': [113.57, 22.27],
    '汕头': [116.68, 23.35],
    '佛山': [113.12, 23.02],
    '江门': [113.08, 22.58],
    '湛江': [110.35, 21.27],
    '茂名': [110.92, 21.67],
    '肇庆': [112.47, 23.05],
    '惠州': [114.42, 23.12],
    '梅州': [116.12, 24.28],
    '汕尾': [115.37, 22.78],
    '河源': [114.7, 23.73],
    '阳江': [111.98, 21.87],
    '清远': [113.03, 23.7],
    '东莞': [113.75, 23.05],
    '中山': [113.38, 22.52],
    '潮州': [116.62, 23.67],
    '揭阳': [116.37, 23.55],
    '云浮': [112.03, 22.92],

    // 广西壮族自治区
    '南宁': [108.37, 22.82],
    '柳州': [109.42, 24.33],
    '防城港': [108.35, 21.7],
    '来宾': [109.23, 23.73],
    '崇左': [107.37, 22.4],
    '桂林': [110.28, 25.28],
    '梧州': [111.27, 23.48],
    '北海': [109.12, 21.48],
    '钦州': [108.62, 21.95],
    '贵港': [109.6, 23.1],
    '玉林': [110.17, 22.63],
    '百色': [106.62, 23.9],
    '贺州': [111.55, 24.42],
    '河池': [108.07, 24.7],

    // 海南省
    '海口': [110.32, 20.03],
    '三亚': [109.5, 18.25],
    '五指山': [109.52, 18.78],
    '琼海': [110.47, 19.25],
    '儋州': [109.57, 19.52],
    '文昌': [110.8, 19.55],
    '万宁': [110.4, 18.8],
    '东方': [108.63, 19.1],
    '定安县': [110.32, 19.7],
    '屯昌县': [110.1, 19.37],
    '澄迈县': [110.0, 19.73],
    '临高县': [109.68, 19.92],
    '白沙黎族自治县': [109.45, 19.23],
    '昌江黎族自治县': [109.05, 19.25],
    '乐东黎族自治县': [109.17, 18.75],
    '陵水黎族自治县': [110.03, 18.5],
    '保亭黎族苗族自治县': [109.7, 18.63],
    '琼中黎族苗族自治县': [109.83, 19.03],

    // 四川省
    '成都': [104.07, 30.67],
    '自贡': [104.78, 29.35],
    '攀枝花': [101.72, 26.58],
    '泸州': [105.43, 28.87],
    '德阳': [104.38, 31.13],
    '绵阳': [104.73, 31.47],
    '广元': [105.83, 32.43],
    '遂宁': [105.57, 30.52],
    '内江': [105.05, 29.58],
    '乐山': [103.77, 29.57],
    '南充': [106.08, 30.78],
    '眉山': [103.83, 30.05],
    '宜宾': [104.62, 28.77],
    '广安': [106.63, 30.47],
    '达州': [107.5, 31.22],
    '雅安': [103.0, 29.98],
    '巴中': [106.77, 31.85],
    '资阳': [104.65, 30.12],
    '阿坝藏族羌族自治州': [102.22, 31.9],
    '甘孜藏族自治州': [101.97, 30.05],
    '凉山彝族自治州': [102.27, 27.9],

    // 贵州省
    '贵阳': [106.63, 26.65],
    '六盘水': [104.83, 26.6],
    '遵义': [106.92, 27.73],
    '安顺': [105.95, 26.25],
    '铜仁': [109.18, 27.72],
    '毕节': [105.28, 27.3],
    '黔东南苗族侗族自治州': [107.97, 26.58],
    '黔南布依族苗族自治州': [107.52, 26.27],

    // 云南省
    '昆明': [102.72, 25.05],
    '曲靖': [103.8, 25.5],
    '玉溪': [102.55, 24.35],
    '保山': [99.17, 25.12],
    '昭通': [103.72, 27.33],
    '丽江': [100.23, 26.88],
    '临沧': [100.08, 23.88],
    '楚雄彝族自治州': [101.55, 25.03],
    '红河哈尼族彝族自治州': [103.4, 23.37],
    '文山壮族苗族自治州': [104.25, 23.37],
    '西双版纳傣族自治州': [100.8, 22.02],
    '大理白族自治州': [100.23, 25.6],
    '德宏傣族景颇族自治州': [98.58, 24.43],
    '怒江傈僳族自治州': [98.85, 25.85],
    '迪庆藏族自治州': [99.7, 27.83],

    // 西藏自治区
    '拉萨': [91.13, 29.65],
    '山南地区': [91.77, 29.23],
    '日喀则': [88.88, 29.27],
    '那曲地区': [92.07, 31.48],
    '阿里地区': [80.1, 32.5],

    // 陕西省
    '西安': [108.93, 34.27],
    '铜川': [108.93, 34.9],
    '宝鸡': [107.13, 34.37],
    '咸阳': [108.7, 34.33],
    '渭南': [109.5, 34.5],
    '延安': [109.48, 36.6],
    '汉中': [107.02, 33.07],
    '榆林': [109.73, 38.28],
    '安康': [109.02, 32.68],
    '商洛': [109.93, 33.87],

    // 甘肃省
    '兰州': [103.82, 36.07],
    '嘉峪关': [98.27, 39.8],
    '金昌': [102.18, 38.5],
    '白银': [104.18, 36.55],
    '天水': [105.72, 34.58],
    '武威': [102.63, 37.93],
    '张掖': [100.45, 38.93],
    '平凉': [106.67, 35.55],
    '酒泉': [98.52, 39.75],
    '庆阳': [107.63, 35.73],
    '定西': [104.62, 35.58],
    '陇南': [104.92, 33.4],
    '临夏回族自治州': [103.22, 35.6],
    '甘南藏族自治州': [102.92, 34.98],

    // 青海省
    '西宁': [101.78, 36.62],
    '黄南藏族自治州': [102.02, 35.52],
    '海南藏族自治州': [100.62, 36.28],
    '果洛藏族自治州': [100.23, 34.48],
    '玉树藏族自治州': [97.02, 33.0],
    '海西蒙古族藏族自治州': [97.37, 37.37],
    '海北藏族自治州': [100.9, 36.97],

    // 直辖市
    '北京': [116.4, 39.9],
    '天津': [117.2, 39.12],
    '上海': [121.47, 31.23],
    '重庆': [106.55, 29.57],

    // 宁夏回族自治区
    '银川': [106.28, 38.47],
    '石嘴山': [106.38, 39.02],
    '吴忠': [106.2, 37.98],
    '固原': [106.28, 36.0],
    '中卫': [105.18, 37.52],

    // 新疆维吾尔自治区
    '乌鲁木齐': [87.62, 43.82],
    '克拉玛依': [84.87, 45.6],
    '吐鲁番': [89.17, 42.95],
    '哈密地区': [93.52, 42.83],
    '昌吉回族自治州': [87.3, 44.02],
    '博尔塔拉蒙古自治州': [82.07, 44.9],
    '巴音郭楞蒙古自治州': [86.15, 41.77],
    '阿克苏地区': [80.27, 41.17],
    '喀什地区': [75.98, 39.47],
    '和田地区': [79.92, 37.12],
    '伊犁哈萨克自治州': [81.32, 43.92],
    '塔城地区': [82.98, 46.75],
    '阿勒泰地区': [88.13, 47.85],
    '石河子': [86.03, 44.3],
    '阿拉尔': [81.28, 40.55],
    '图木舒克': [79.13, 39.85],
    '五家渠': [87.53, 44.17],
    '大理': [99.97, 25.42],

    // 阿富汗主要城市
    '喀布尔': [69.2075, 34.5553],
    '坎大哈': [65.7372, 31.6080],
    '赫拉特': [62.1950, 34.3482],
    '马扎里沙里夫': [67.1108, 36.7090],
    '昆都士': [68.8575, 36.7289],
    '贾拉拉巴德': [70.4531, 34.4257],
    '拉什卡尔加': [64.3721, 31.5830],
    '塔洛坎': [69.5347, 36.7311],
    '加兹尼': [68.4172, 33.5539],
    '巴米扬': [67.8270, 34.8167],
    '法拉': [62.1161, 32.3744],
    '法扎巴德': [70.5839, 37.1167],
    '霍斯特': [69.9272, 33.3394],
    '查里卡尔': [69.1711, 35.0131],
    '梅赫塔兰': [65.2792, 32.7731],
    '巴格兰': [68.7086, 36.1331],
    '萨尔普勒': [66.3306, 36.2156],
    '迈马纳': [64.7739, 35.9239],
    '卡拉特': [66.8978, 32.1056],
    '加德兹': [69.2561, 33.5931],
    '昆都兹': [68.8575, 36.7289],
    '巴达赫尚': [70.8119, 36.7347],
    '潘杰希尔': [69.3167, 35.3500],
    '瓦尔达克': [68.1500, 34.3667],
    '洛加尔': [69.0167, 34.0167],
    '帕克蒂亚': [69.3833, 33.7000],
    '帕克蒂卡': [68.7833, 32.2667],
    '楠格哈尔': [70.1833, 34.1667],
    '拉格曼': [70.1333, 34.6833],
    '努里斯坦': [70.9167, 35.3167],
    '库纳尔': [71.0833, 35.0000],
    '卡皮萨': [69.6167, 35.0167],
    '帕尔万': [69.1167, 35.2500],
    '萨曼甘': [68.0667, 36.3167],
    '巴尔赫': [66.8833, 36.7500],
    '朱兹詹': [65.6667, 36.8667],
    '萨里普勒': [66.3306, 36.2156],
    '法里亚布': [64.9167, 35.8833],
    '巴德吉斯': [63.1833, 35.1667],
    '古尔': [64.5500, 33.1167],
    '乌鲁兹甘': [66.6333, 32.9333],
    '扎布尔': [67.3500, 32.3167],
    '坎大哈省': [65.7372, 31.6080],
    '赫尔曼德': [64.3667, 31.5833],
    '尼姆鲁兹': [61.9500, 31.0167],

    // 阿尔巴尼亚主要城市
    '地拉那': [19.8187, 41.3275],
    '都拉斯': [19.4444, 41.3225],
    '发罗拉': [19.4950, 40.4642],
    '斯库台': [19.5122, 42.0683],
    '爱尔巴桑': [20.0822, 41.1125],
    '科尔察': [20.7881, 40.6186],
    '费里': [20.7631, 40.9306],
    '卢什涅': [19.7058, 40.9422],
    '卡瓦亚': [19.5569, 41.1856],
    '吉诺卡斯特': [20.1394, 40.0756],
    '萨兰达': [19.9994, 39.8747],
    '库克斯': [20.4211, 42.0775],
    '莱什': [19.6439, 41.7836],
    '贝拉特': [19.9522, 40.7058],
    '波格拉德茨': [20.6533, 41.0186],
    '帕托斯': [19.6206, 40.6831],
    '拉奇': [19.4331, 41.2331],
    '布尔奇扎': [20.0089, 41.8831],
    '马穆拉斯': [19.6889, 41.5831],
    '佩金': [19.7831, 40.9331],
    '罗戈日纳': [19.8831, 41.0831],
    '克鲁亚': [19.7931, 41.5089],
    '普雷梅蒂': [19.9331, 41.3331],
    '马蒂': [19.6331, 41.4831],
    '迪韦尔': [19.5831, 41.0331],
    '希贾克': [19.5631, 41.3331],
    '沃拉': [19.4631, 40.7331],
    '塞莱尼察': [19.6431, 40.5331],
    '马拉卡斯特拉': [19.4331, 40.5831],
    '佩尔梅特': [20.3531, 40.2331],

    // 阿尔及利亚主要城市
    '阿尔及尔': [3.0588, 36.7538],
    '奥兰': [0.6405, 35.6911],
    '君士坦丁': [6.6147, 36.3650],
    '安纳巴': [7.7669, 36.9000],
    '布利达': [2.8277, 36.4203],
    '巴特纳': [6.1742, 35.5559],
    '塞提夫': [5.4107, 36.1900],
    '锡迪贝勒阿贝斯': [-0.6298, 35.1977],
    '比斯克拉': [5.7244, 34.8481],
    '蒂济乌祖': [4.0591, 36.7118],
    '瓦尔格拉': [5.3317, 31.9539],
    '贝贾亚': [5.0755, 36.7525],
    '蒂亚雷特': [1.3170, 35.3711],
    '塔曼拉塞特': [5.5228, 22.7851],
    '贝沙尔': [-2.2167, 31.6167],
    '乌阿尔格拉': [5.3317, 31.9539],
    '斯基克达': [6.9147, 36.8761],
    '萨伊达': [0.1514, 34.8425],
    '穆斯塔加奈姆': [0.0892, 35.9315],
    '梅德亚': [2.7539, 36.2639],
    '雷利赞': [0.5569, 35.7322],
    '马斯卡拉': [0.1406, 35.3969],
    '乌姆布瓦吉': [1.4839, 35.8553],
    '索克阿赫拉斯': [7.9514, 36.2864],
    '蒂巴扎': [2.4439, 36.5939],
    '米拉': [6.2639, 36.4539],
    '吉杰勒': [5.7669, 36.8200],
    '阿德拉尔': [0.2939, 27.8739],
    '拉格瓦特': [2.8639, 33.8039],
    '盖尔达耶': [3.6739, 32.4939],
    '伊利济': [8.4739, 26.5039],
    '廷杜夫': [-8.1261, 27.6739],
    '艾因德夫拉': [2.9339, 36.2639],
    '布伊拉': [3.9039, 36.3739],
    '塔里夫': [0.3239, 35.3739],
    '艾因特穆尚特': [5.6339, 35.3039],
    '提塞姆西勒特': [1.8139, 35.6039],
    '埃尔巴亚德': [0.1639, 32.7639],
    '纳阿马': [-0.3061, 33.2639],
    '艾因萨拉赫': [2.4639, 27.2039],
    '盖尔马': [8.2239, 36.8639],
    '布尔杰布德雷乌伊': [4.7639, 36.0739],
    '蒂米蒙': [0.2339, 29.2639],
    '边界': [7.0839, 34.8439],
    '托格尔特': [6.0639, 33.1039],

    // 美属萨摩亚主要城市和村庄
    '帕果帕果': [-170.7025, -14.2781],
    '法加托戈': [-170.7114, -14.2947],
    '努乌利': [-170.7181, -14.2892],
    '马拉埃洛阿': [-170.6892, -14.2725],
    '利昂': [-170.7847, -14.3392],
    '瓦伊洛阿': [-170.6725, -14.2558],
    '帕瓦': [-170.6558, -14.2392],
    '阿拉奥': [-170.6392, -14.2225],
    '阿福诺': [-170.8225, -14.3558],
    '波拉': [-170.8392, -14.3725],
    '阿乌阿': [-170.8558, -14.3892],
    '阿马卢拉': [-170.8725, -14.4058],
    '阿奥洛阿乌': [-170.8892, -14.4225],
    '法莱阿塞拉': [-170.9058, -14.4392],
    '马萨福': [-170.9225, -14.4558],
    '马萨弗阿': [-170.9392, -14.4725],
    '塔乌': [-169.5392, -14.2392],
    '菲蒂乌塔': [-169.4225, -14.1725],
    '法拉塞': [-169.3558, -14.1392],
    '奥福': [-169.6725, -14.2892],
    '奥洛塞加': [-169.6225, -14.2725],
    '西乌法加': [-169.3058, -14.1225],
    '阿拉奥法': [-169.2725, -14.1058],
    '法拉洛普': [-169.2392, -14.0892],
    '马乌': [-169.2058, -14.0725],

    // 安道尔主要城市和村镇
    '安道尔城': [1.5218, 42.5063],
    '埃斯卡尔德斯-恩戈尔达尼': [1.5347, 42.5069],
    '恩坎普': [1.5831, 42.5297],
    '圣胡利娅德洛里亚': [1.4914, 42.4631],
    '拉马萨纳': [1.5147, 42.5447],
    '奥尔迪诺': [1.5331, 42.5564],
    '卡尼略': [1.5997, 42.5681],
    '阿林萨尔': [1.4831, 42.5714],
    '帕尔': [1.4997, 42.5831],
    '拉科尔蒂纳达': [1.5164, 42.5947],
    '索尔德乌': [1.5664, 42.5781],
    '埃尔塔特': [1.5497, 42.5614],
    '梅里特克塞尔': [1.5081, 42.5181],
    '圣科洛马': [1.4747, 42.4897],
    '安道尔拉韦利亚': [1.4664, 42.4764],
    '埃尔塞拉特': [1.5414, 42.5414],
    '格劳罗伊格': [1.6081, 42.5831],
    '拉马尔加里达': [1.5747, 42.5697],
    '埃尔帕斯德拉卡萨': [1.5581, 42.5564],
    '博尔达': [1.5247, 42.5297],
    '圣安东尼': [1.4914, 42.5064],
    '埃斯卡斯': [1.4581, 42.4831],
    '拉莫雷拉': [1.4414, 42.4697],
    '纳加罗': [1.4247, 42.4564],
    '托斯': [1.4081, 42.4431],

    // 安哥拉主要城市
    '罗安达': [13.2302, -8.8383],
    '万博': [13.3644, -12.5022],
    '洛比托': [13.5403, -12.3644],
    '本格拉': [13.4058, -12.5764],
    '卡宾达': [12.2019, 5.5550],
    '马兰热': [13.8269, -9.2669],
    '卢班戈': [13.4925, -14.9175],
    '索约': [12.3689, 5.7019],
    '纳米贝': [12.1522, -15.1958],
    '门东萨': [18.2625, -9.2808],
    '卡齐古': [17.4519, -7.8169],
    '恩达拉坦多': [13.8558, -11.4558],
    '卡拉': [16.8844, -11.6844],
    '卢埃纳': [19.9169, -11.7831],
    '萨乌里莫': [20.3919, -9.6669],
    '苏马贝': [13.8419, -11.2069],
    '恩泽托': [13.2769, -7.2169],
    '卡卢凯梅贝': [16.3419, -8.4569],
    '卡巴萨': [17.4269, -12.4869],
    '库伊托': [16.9419, -12.3769],
    '安达卢': [13.7669, -9.5569],
    '卡拉库洛': [13.4919, -9.2869],
    '卡塔班加': [24.2769, -11.4969],
    '卢卡帕': [18.2419, -14.5869],
    '奥尼瓦': [15.7269, -17.0669],
    '托姆布瓦': [16.1419, -15.2469],
    '恩泽雷': [12.8769, 6.0419],
    '布科扎乌': [12.6169, 5.4069],
    '兰达纳': [12.1319, 5.2569],
    '卡库洛': [12.4069, 5.6319],
    '马萨比': [12.7419, 5.8769],
    '蒂巴': [12.5669, 5.7569],
    '米科诺戈': [12.3419, 5.4819],
    '恩库卢': [12.8069, 5.9569],
    '乌伊热': [15.0569, -5.5869],
    '多恩多': [18.7419, -7.3769],
    '卡拉卡伊': [20.6769, -8.4169],
    '卡松戈': [16.8169, -9.1869],
    '恩希诺': [15.2069, -15.7569],

    // 安圭拉岛主要城市和村庄
    '瓦利': [-63.0578, 18.2206],
    '南山': [-63.0578, 18.1706],
    '乔治山': [-63.0278, 18.2006],
    '岛港': [-63.0878, 18.2306],
    '布洛因点': [-63.1378, 18.2506],
    '西端': [-63.1678, 18.2406],
    '长湾': [-63.1078, 18.1906],
    '肖尔湾': [-63.0778, 18.1606],
    '森迪格朗德': [-63.0478, 18.1806],
    '森迪希尔': [-63.0178, 18.1906],
    '克罗库斯湾': [-63.0978, 18.2106],
    '利特尔湾': [-63.0678, 18.2006],
    '巴恩斯湾': [-63.1178, 18.2206],
    '米德湾': [-63.1278, 18.2306],
    '伦德维尤': [-63.0378, 18.1706],
    '斯托尼格朗德': [-63.0078, 18.1806],
    '东端': [-62.9778, 18.2106],
    '岛港村': [-63.0878, 18.2306],
    '北山': [-63.0478, 18.2406],
    '北侧': [-63.0578, 18.2506],
    '南侧': [-63.0678, 18.1506],
    '中央': [-63.0378, 18.1906],
    '海滨': [-63.0778, 18.2006],
    '高地': [-63.0278, 18.2106],
    '低地': [-63.0878, 18.1806],

    // 南极洲主要科研站和定居点
    '麦克默多站': [166.6681, -77.8419],
    '阿蒙森-斯科特南极站': [0.0000, -90.0000],
    '罗瑟拉研究站': [-57.0000, -67.5667],
    '哈雷研究站': [-26.2167, -75.5833],
    '中山站': [76.3694, -69.3733],
    '长城站': [-58.9583, -62.2167],
    '昭和基地': [39.5833, -69.0000],
    '沃斯托克站': [106.8372, -78.4647],
    '贝尔格拉诺二号基地': [-34.6333, -77.8667],
    '埃斯佩兰萨基地': [-57.0000, -63.4000],
    '马兰比奥基地': [-56.6167, -64.2333],
    '圣马丁基地': [-67.1167, -68.1333],
    '奥希金斯基地': [-57.9000, -63.3167],
    '弗雷总统基地': [-58.9833, -62.2000],
    '贝拉诺基地': [-38.9167, -77.9667],
    '杜蒙杜维尔站': [140.0167, -66.6667],
    '凯西站': [110.5167, -66.2833],
    '戴维斯站': [77.9667, -68.5833],
    '莫森站': [62.8667, -67.6000],
    '马卡里研究站': [158.9500, -54.5000],
    '斯科特基地': [166.7667, -77.8500],
    '阿根廷群岛基地': [-65.2500, -65.2500],
    '帕尔默站': [-64.0500, -64.7667],
    '韦尔纳德斯基研究站': [-65.2500, -65.2500],
    '贝尔林斯高晋站': [-58.9667, -62.2000],
    '诺伊迈尔站': [-8.2667, -70.6500],
    '桑艾研究站': [-2.3500, -70.8333],
    '特罗尔研究站': [2.5333, -72.0167],
    '哥德堡研究站': [-8.2500, -70.6333],
    '瓦萨研究站': [11.8333, -73.0500],
    '贝克岛研究站': [-68.1333, -67.6000],
    '罗滕研究站': [-68.8333, -67.5667],
    '彼得曼岛研究站': [-65.1667, -65.1667],
    '法拉第研究站': [-65.2500, -65.2500],
    '阿德莱德岛研究站': [-68.9000, -67.7667],
    '亚历山大岛研究站': [-70.9333, -69.4667],
    '埃尔斯沃思研究站': [-83.1167, -79.2333],
    '拜尔德研究站': [-120.0000, -80.0167],
    '西摩岛研究站': [-56.7333, -64.2833],
    '雪山岛研究站': [-61.0833, -62.4833],

    // 安提瓜和巴布达主要城市和村庄
    '圣约翰': [-61.8456, 17.1211],
    '奥尔德伯里': [-61.8156, 17.0711],
    '利伯塔': [-61.7856, 17.0411],
    '博兰斯': [-61.8756, 17.0911],
    '帕勒姆': [-61.8056, 17.1011],
    '皮戈茨': [-61.8356, 17.1311],
    '弗里敦': [-61.7656, 17.0611],
    '英国港': [-61.9056, 17.0011],
    '法尔茅斯': [-61.8256, 17.0211],
    '威洛比湾': [-61.7756, 17.1111],
    '五岛村': [-61.8656, 17.0811],
    '约翰休斯': [-61.7956, 17.0811],
    '新分部': [-61.8456, 17.0511],
    '旧路': [-61.8156, 17.0311],
    '克拉布山': [-61.7856, 17.1211],
    '塞达格罗夫': [-61.8556, 17.1411],
    '詹宁斯': [-61.8256, 17.1511],
    '乔利港': [-61.8956, 17.0711],
    '迪克森': [-61.7556, 17.0911],
    '贝蒂霍普': [-61.8756, 17.1111],
    '科德林顿': [-61.8256, 17.6311],
    '巴布达村': [-61.8156, 17.6211],
    '杜尔西': [-61.8356, 17.6411],
    '河湾': [-61.8056, 17.6111],
    '高地': [-61.8456, 17.6511],
    '低湾': [-61.8556, 17.6011],
    '棕榈滩': [-61.7856, 17.6311],
    '粉红沙滩': [-61.7756, 17.6411],
    '西点': [-61.8656, 17.6211],
    '东点': [-61.7656, 17.6111],

    // 阿根廷主要城市
    '布宜诺斯艾利斯': [-58.3816, -34.6037],
    '科尔多瓦': [-64.1811, -31.4201],
    '罗萨里奥': [-60.6393, -32.9442],
    '门多萨': [-68.8458, -32.8895],
    '图库曼': [-65.2226, -26.8083],
    '拉普拉塔': [-57.9544, -34.9215],
    '马德普拉塔': [-57.5759, -38.0055],
    '萨尔塔': [-65.4167, -24.7821],
    '圣胡安': [-68.5364, -31.5375],
    '雷西斯滕西亚': [-58.9833, -27.4606],
    '圣菲': [-60.7069, -31.6333],
    '科连特斯': [-58.8344, -27.4806],
    '巴拉那': [-60.5236, -31.7319],
    '波萨达斯': [-55.8961, -27.3621],
    '内乌肯': [-68.0591, -38.9516],
    '里奥加耶戈斯': [-69.2167, -51.6167],
    '乌斯怀亚': [-68.3000, -54.8000],
    '科莫多罗里瓦达维亚': [-67.5000, -45.8667],
    '巴里洛切': [-71.3000, -41.1333],
    '萨帕拉': [-67.7167, -19.0333],
    '胡胡伊': [-65.3000, -24.1833],
    '里奥夸尔托': [-64.3500, -33.1333],
    '圣路易斯': [-66.3333, -33.3000],
    '拉里奥哈': [-66.8500, -29.4333],
    '卡塔马卡': [-65.7833, -28.4667],
    '圣地亚哥德尔埃斯特罗': [-64.2667, -27.7833],
    '福莫萨': [-58.1833, -26.1833],
    '拉潘帕': [-64.2833, -36.6167],
    '丘布特': [-65.3000, -43.2500],
    '圣克鲁斯': [-68.5333, -50.0167],
    '火地岛': [-68.3000, -54.8000],
    '布兰卡港': [-62.2667, -38.7167],
    '圣尼古拉斯': [-60.2167, -33.3333],
    '萨拉特': [-58.5833, -31.7833],
    '康塞普西翁德尔乌拉圭': [-58.2333, -32.4833],
    '圣拉斐尔': [-68.3333, -34.6167],
    '里韦拉达维亚': [-67.6333, -33.1833],
    '圣卡洛斯德巴里洛切': [-71.3000, -41.1333],
    '特雷利乌': [-65.3000, -43.2500],
    '埃斯克尔': [-71.3167, -42.9167],
    '马德林港': [-65.0333, -42.7667],
    '卡拉法特': [-72.2667, -50.3333],
    '里奥图尔比奥': [-72.2500, -51.5333],
    '皮科特伦卡': [-71.2833, -40.0833],
    '萨帕拉': [-67.7167, -19.0333],
    '奥兰': [-64.3167, -23.1333],

    // 亚美尼亚主要城市
    '埃里温': [44.5152, 40.1792],
    '久姆里': [43.8456, 40.7894],
    '瓦纳佐尔': [44.4939, 40.8058],
    '卡潘': [46.4069, 39.2069],
    '阿拉韦尔迪': [44.2831, 40.9831],
    '阿什塔拉克': [44.3606, 40.2939],
    '戈里斯': [46.3419, 39.5081],
    '阿尔马维尔': [43.9939, 40.1539],
    '阿尔塔沙特': [44.5539, 39.9539],
    '阿贝戈夫扬': [44.2706, 40.2106],
    '塞万': [44.9439, 40.5539],
    '伊杰万': [45.1539, 40.8839],
    '马拉利克': [44.1206, 40.9706],
    '塔林': [43.8739, 40.3739],
    '埃奇米阿津': [44.2939, 40.1639],
    '梅格里': [46.2439, 38.9039],
    '卡法恩': [45.0139, 39.6439],
    '西西安': [46.0439, 39.1739],
    '阿格塔克': [44.8139, 40.0339],
    '巴格拉马什恩': [44.1839, 40.4339],
    '查伦茨阿万': [44.7239, 40.1939],
    '埃格瓦尔德': [44.2339, 40.3739],
    '加尔尼': [44.7339, 40.1139],
    '霍尔维拉普': [44.5739, 39.8739],
    '诺拉万克': [45.2339, 39.6839],
    '塔特夫': [46.2439, 39.3739],
    '叶赫格纳佐尔': [46.1039, 39.3539],
    '斯捷潘纳万': [44.3739, 40.8439],
    '阿帕兰': [44.3539, 40.5939],
    '阿尔塔瓦兹德': [44.6939, 40.2739],
    '阿克塔拉': [44.1539, 40.5639],

    // 阿鲁巴岛主要城市和地区
    '奥拉涅斯塔德': [-70.0270, 12.5186],
    '圣尼古拉斯': [-69.9089, 12.4364],
    '诺德': [-70.0389, 12.5689],
    '棕榈滩': [-70.0489, 12.5789],
    '鹰滩': [-70.0189, 12.5589],
    '曼彻博': [-69.9789, 12.4989],
    '萨瓦内塔': [-69.9689, 12.4789],
    '圣克鲁斯': [-69.9889, 12.5089],
    '帕拉德拉': [-69.9589, 12.4589],
    '布拉西托': [-69.9389, 12.4389],
    '达科塔': [-69.9289, 12.4289],
    '德鲁伊夫': [-70.0589, 12.5889],
    '哈迪库里': [-70.0689, 12.5989],
    '马尔默克': [-70.0789, 12.6089],
    '阿拉希': [-69.9189, 12.4189],
    '巴卡': [-69.9089, 12.4089],
    '科迪贝': [-69.8989, 12.3989],
    '弗伦奇曼帕斯': [-69.8889, 12.3889],
    '伊马达': [-69.8789, 12.3789],
    '库达里贝': [-69.8689, 12.3689],
    '拉戈': [-69.8589, 12.3589],
    '米拉马尔': [-69.8489, 12.3489],
    '尼乌韦克尔克': [-69.8389, 12.3389],
    '奥斯特普恩特': [-69.8289, 12.3289],
    '普拉亚': [-69.8189, 12.3189],
    '罗卡': [-69.8089, 12.3089],
    '圣巴巴拉': [-69.7989, 12.2989],
    '塔纳基': [-69.7889, 12.2889],
    '韦斯特普恩特': [-70.0889, 12.6189],
    '扎乌特潘': [-69.7789, 12.2789],

    // 澳大利亚主要城市
    '悉尼': [151.2093, -33.8688],
    '墨尔本': [144.9631, -37.8136],
    '布里斯班': [153.0281, -27.4678],
    '珀斯': [115.8605, -31.9505],
    '阿德莱德': [138.6007, -34.9285],
    '堪培拉': [149.1300, -35.2809],
    '达尔文': [130.8456, -12.4634],
    '霍巴特': [147.3272, -42.8821],
    '黄金海岸': [153.4000, -28.0167],
    '纽卡斯尔': [151.7817, -32.9283],
    '卧龙岗': [150.8931, -34.4278],
    '阳光海岸': [153.0667, -26.6500],
    '吉朗': [144.3600, -38.1500],
    '汤斯维尔': [146.8169, -19.2590],
    '凯恩斯': [145.7781, -16.9186],
    '图文巴': [151.9500, -27.5667],
    '巴拉瑞特': [143.8503, -37.5622],
    '本迪戈': [144.2794, -36.7570],
    '奥尔伯里': [146.9167, -36.0833],
    '莱顿': [146.0500, -38.1833],
    '罗克汉普顿': [150.5069, -23.3781],
    '邦德堡': [152.3544, -24.8661],
    '赫维湾': [152.8544, -25.2986],
    '麦凯': [149.1869, -21.1550],
    '格拉德斯通': [151.2569, -23.8469],
    '卡尔古利': [121.4733, -30.7494],
    '杰拉尔顿': [114.6144, -28.7774],
    '奥尔巴尼': [117.8844, -35.0269],
    '班伯里': [115.6644, -33.3269],
    '布鲁姆': [122.2344, -17.9669],
    '卡拉萨': [116.7844, -20.7369],
    '德比': [123.6344, -17.3069],
    '纽曼': [119.7344, -23.3569],
    '汤姆普赖斯': [117.7844, -22.6969],
    '黑德兰港': [118.5844, -20.3169],
    '布鲁克': [127.1344, -17.9469],
    '库努纳拉': [128.7344, -15.7769],
    '凯瑟琳': [132.2644, -14.4669],
    '爱丽斯泉': [133.8806, -23.6980],
    '乌鲁鲁': [131.0369, -25.3444],
    '库伯佩迪': [134.7544, -29.0144],
    '怀阿拉': [137.5844, -31.5544],
    '塞杜纳': [133.6844, -32.1244],
    '林肯港': [135.8644, -34.7244],
    '芒特甘比尔': [140.7844, -37.8244],

    // 奥地利主要城市
    '维也纳': [16.3738, 48.2082],
    '格拉茨': [15.4395, 47.0707],
    '林茨': [14.2858, 48.3069],
    '萨尔茨堡': [13.0550, 47.8095],
    '因斯布鲁克': [11.4041, 47.2692],
    '克拉根福': [14.3050, 46.6240],
    '维尔斯': [14.0297, 48.1598],
    '多恩比恩': [9.7417, 47.4125],
    '维纳新城': [16.2426, 47.8175],
    '施泰尔': [14.4214, 48.0458],
    '费尔德基希': [9.6007, 47.2333],
    '布雷根茨': [9.7471, 47.5058],
    '莱昂丁': [14.2542, 48.2958],
    '科恩新堡': [16.3308, 48.3500],
    '沃尔夫斯贝格': [14.8442, 46.8383],
    '巴登': [16.2308, 47.9975],
    '莫德林': [16.2875, 48.0842],
    '特劳恩': [14.2333, 48.2167],
    '卡普芬贝格': [15.2958, 47.4458],
    '卢斯滕瑙': [9.6625, 47.4292],
    '哈勒因': [13.0958, 47.6833],
    '库夫施泰因': [12.1667, 47.5833],
    '施韦夏特': [16.4708, 48.1375],
    '施皮塔尔安德劳': [13.4958, 46.8000],
    '特尔尼茨': [16.0458, 47.9167],
    '安斯费尔登': [14.2875, 48.2042],
    '莱奥本': [15.0958, 47.3833],
    '维拉赫': [13.8542, 46.6167],
    '圣珀尔滕': [15.6250, 48.2042],
    '霍恩埃姆斯': [9.6875, 47.2458],
    '布鲁克安德穆尔': [15.2625, 47.4167],
    '克雷姆斯': [15.6167, 48.4167],
    '兰德克': [10.5667, 47.1333],
    '比绍夫斯霍芬': [13.0792, 47.4167],
    '埃贝尔斯贝格': [14.3458, 48.3375],
    '巴特伊舍尔': [13.6167, 47.7167],
    '格蒙登': [13.7958, 47.9167],
    '瓦滕斯': [11.5833, 47.2833],
    '阿尔滕马克特': [13.7792, 47.8792],
    '蒂罗尔州哈尔': [11.5167, 47.2833],

    // 阿塞拜疆主要城市
    '巴库': [49.8671, 40.4093],
    '占贾': [46.3606, 40.6828],
    '苏姆盖特': [49.6692, 40.5897],
    '明盖恰乌尔': [47.0581, 40.7631],
    '库尔达米尔': [48.0581, 40.3431],
    '舍基': [47.1706, 41.1939],
    '叶夫拉赫': [47.1039, 40.6181],
    '连科兰': [48.8506, 38.7539],
    '纳希切万': [45.4106, 39.2089],
    '加巴拉': [47.8506, 40.9789],
    '巴尔达': [47.1306, 40.3789],
    '阿格达什': [47.4706, 40.6489],
    '贝拉甘': [47.4106, 41.4089],
    '比拉苏瓦尔': [48.5506, 39.4589],
    '达什克桑': [46.0806, 40.5189],
    '菲祖利': [47.1506, 39.6089],
    '戈布斯坦': [49.0106, 40.5389],
    '戈伊恰伊': [47.7406, 40.6589],
    '哈奇马兹': [48.8006, 41.4689],
    '伊米什利': [48.0606, 39.8789],
    '伊斯马伊利': [48.1506, 40.7889],
    '卡尔巴贾尔': [46.0406, 40.1089],
    '霍贾文德': [47.0906, 39.5089],
    '拉钦': [46.5806, 39.9189],
    '拉希兹': [48.4206, 41.0189],
    '马萨利': [48.6606, 39.0389],
    '奈夫特恰拉': [49.2506, 40.6789],
    '奥格兹': [45.0506, 41.0789],
    '古巴': [48.5106, 41.3689],
    '古萨尔': [48.4306, 41.3589],
    '萨比拉巴德': [48.4806, 40.0089],
    '萨拉赫': [45.3606, 39.8089],
    '萨姆基尔': [46.4206, 40.7689],
    '沙马基': [48.6406, 40.6289],
    '沙姆科尔': [45.9206, 40.8289],
    '舒沙': [46.7506, 39.7589],
    '西亚赞': [49.1106, 40.3789],
    '塔尔塔尔': [47.3406, 40.3389],
    '托武兹': [45.6306, 40.9889],
    '乌贾尔': [47.6506, 40.5189],
    '扎卡塔拉': [46.6506, 41.6389],
    '赞格兰': [46.6606, 39.0889],
    '扎尔达布': [47.7006, 40.2089],
    '阿斯塔拉': [48.8706, 38.4589],
    '朱尔法': [45.6306, 38.8589],

    // 巴哈马主要城市和岛屿
    '拿骚': [-77.3504, 25.0443],
    '自由港': [-78.7000, 26.5333],
    '库珀斯敦': [-77.5167, 25.6667],
    '邓莫尔镇': [-76.6333, 25.5000],
    '乔治敦': [-75.7833, 23.5167],
    '阿瑟镇': [-74.5333, 22.3667],
    '马修镇': [-77.8333, 25.9167],
    '尼古拉斯镇': [-77.8833, 25.0500],
    '安德罗斯镇': [-77.7667, 24.7000],
    '凯姆普湾': [-77.8167, 24.3167],
    '马纳蒂点': [-77.6833, 24.4333],
    '红湾': [-77.5333, 25.7167],
    '弗雷什克里克': [-77.7000, 24.7333],
    '科克本镇': [-74.5167, 21.4667],
    '南卡库斯': [-71.9500, 21.9500],
    '霍克斯比尔': [-74.1833, 22.4000],
    '洛里默斯': [-74.3667, 22.2000],
    '斯特拉港': [-77.0167, 24.1667],
    '高岩': [-77.0333, 26.6167],
    '西端': [-78.9833, 26.6833],
    '八英里岩': [-78.2333, 26.5833],
    '卢卡亚': [-78.6667, 26.5333],
    '佩利坎点': [-78.4167, 26.5667],
    '松树岭': [-78.1000, 26.5167],
    '史密斯点': [-78.2167, 26.7167],
    '水湾': [-78.3500, 26.6000],
    '海滨高地': [-78.2833, 26.5500],
    '卢卡亚海滩': [-78.6833, 26.5167],
    '塔伊诺海滩': [-78.3167, 26.5833],
    '金岩溪': [-78.2500, 26.6333],
    '巴哈马岩': [-77.8667, 25.0833],
    '卡布里奇': [-77.7833, 25.0167],
    '卡梅尔': [-77.4167, 25.0833],
    '福克斯山': [-77.4833, 25.0667],
    '南海滩': [-77.3167, 25.0167],
    '西湾': [-77.4667, 25.0500],
    '阿德莱德': [-77.5167, 25.0333],
    '格兰特镇': [-77.3833, 25.0667],
    '天堂岛': [-77.3167, 25.0833],
    '电缆海滩': [-77.3333, 25.0500],

    // 巴林主要城市和地区
    '麦纳麦': [50.5876, 26.2285],
    '穆哈拉格': [50.6119, 26.2572],
    '里法': [50.5500, 26.1300],
    '哈马德镇': [50.4833, 26.1167],
    '伊萨镇': [50.5167, 26.1667],
    '锡特拉': [50.6167, 26.1500],
    '吉德哈夫斯': [50.4500, 26.2333],
    '阿瓦利': [50.5833, 26.2167],
    '布代雅': [50.4667, 26.2500],
    '萨尔曼尼亚': [50.5333, 26.2000],
    '杜拉兹': [50.6333, 26.2833],
    '阿拉德': [50.6500, 26.2500],
    '贾乌': [50.4167, 26.2167],
    '萨基尔': [50.4333, 26.0833],
    '阿卜杜拉港': [50.6167, 26.2333],
    '哈利法港': [50.6000, 26.2667],
    '马尼法': [50.5667, 26.1833],
    '卡尔巴巴德': [50.4500, 26.0500],
    '阿西卡尔': [50.5500, 26.0667],
    '迪亚尔': [50.5000, 26.0500],
    '津巴利': [50.4833, 26.0667],
    '阿卜杜拉国王经济城': [50.5167, 26.0833],
    '巴林湾': [50.6333, 26.3000],
    '北部省': [50.5000, 26.3167],
    '南部省': [50.4667, 26.0333],
    '中部省': [50.5333, 26.1500],
    '穆哈拉格省': [50.6167, 26.2667],
    '首都省': [50.5833, 26.2333],
    '巴林国际机场': [50.6333, 26.2667],
    '巴林金融港': [50.5833, 26.2500],
    '巴林世界贸易中心': [50.5833, 26.2333],
    '阿马瓦吉群岛': [50.7667, 26.2500],
    '哈瓦尔群岛': [50.8000, 25.6667],
    '乌姆纳桑岛': [50.8167, 26.2833],
    '锡特拉岛': [50.6167, 26.1500],

    // 孟加拉国主要城市
    '达卡': [90.4125, 23.8103],
    '吉大港': [91.8317, 22.3569],
    '库尔纳': [89.5403, 22.8456],
    '拉杰沙希': [88.6042, 24.3745],
    '锡尔赫特': [91.8833, 24.8917],
    '朗布尔': [89.2444, 25.1558],
    '巴里萨尔': [90.3536, 22.7010],
    '迈门辛赫': [90.4203, 24.7471],
    '科米拉': [91.1833, 23.4607],
    '纳拉扬甘杰': [90.4999, 23.6238],
    '加济布尔': [90.4203, 23.9999],
    '通吉': [89.9378, 23.7808],
    '博格拉': [89.3697, 24.8510],
    '杰索尔': [89.2081, 23.1667],
    '帕布纳': [89.2372, 24.0064],
    '迪纳杰布尔': [88.6370, 25.6217],
    '马达里布尔': [90.1896, 23.1641],
    '纳拉扬甘杰': [90.4999, 23.6238],
    '法里德布尔': [89.8429, 23.6070],
    '库什蒂亚': [89.1200, 23.9013],
    '萨特基拉': [89.0700, 22.7185],
    '诺阿卡利': [91.0995, 22.8696],
    '费尼': [91.3972, 23.0144],
    '拉克什米布尔': [90.8412, 22.9447],
    '钱德布尔': [90.6412, 23.2333],
    '布拉曼巴里亚': [91.1119, 23.9570],
    '霍比甘杰': [90.0831, 23.7925],
    '纳尔辛迪': [90.7153, 23.9229],
    '马尼克甘杰': [90.0047, 23.8644],
    '穆恩希甘杰': [90.5312, 23.5513],
    '沙里亚特布尔': [90.4348, 23.2423],
    '戈帕尔甘杰': [89.8266, 23.0050],
    '马达里布尔': [90.1896, 23.1641],
    '拉杰巴里': [89.6444, 23.7574],
    '库马尔卡利': [89.1311, 23.5975],
    '贾洛卡蒂': [90.1870, 22.6406],
    '皮罗杰布尔': [89.9720, 22.5840],
    '博拉': [90.3592, 22.4709],
    '帕图阿卡利': [90.3492, 22.3596],
    '巴尔古纳': [89.9700, 22.1590],
    '朱阿卡利': [90.1870, 22.6406],
    '班达尔班': [92.2047, 22.1953],
    '兰加马蒂': [92.2047, 22.6533],
    '考克斯巴扎尔': [91.9794, 21.4272],
    '特克纳夫': [92.3058, 20.8833]
  },
  showCityNumber: 5,
  showCount: 0,
  isLoading: true
}

const getters = {}



const actions = {
  fetchHeatChinaRealData({ state, commit }, chartsObj) {
    let isPreview = 1;
    if (isPreview == 1) {
      let data = chartsObj.data;
      let paleData = ((state, data) => {
        let arr = []
        let len = data.length
        while (len--) {
          let geoCoord = state.geoCoordMap[data[len].name]
          if (geoCoord) {
            arr.push({
              name: data[len].name,
              value: geoCoord.concat(data[len].value)
            })
          }
        }
        return arr
      })(state, data)
      let lightData = paleData.sort((a, b) => {
        return b.value[2] - a.value[2]
      }).slice(0, 5)
      if (state.isLoading) {
        chartsObj.chinaMap.hideLoading()
        commit('closeLoading')
      }
      chartsObj.chinaMap.setOption({
        series: [
          {
            name: '地区热度',
            data: paleData
          },
          {
            name: 'top5',
            data: lightData
          }
        ]
      })
    } else {

    }
  }
}

const mutations = {
  closeLoading(state) {
    state.isLoading = false
  },
  openLoading(state) {
    state.isLoading = true
  },
  // addCount (state) {
  //   state.showCount ++
  //   // test
  //   if (state.showCount >= 70) {
  //     state.showCount = 1
  //   }
  // }
}

export default {
  state,
  getters,
  actions,
  mutations
}
