<template>
    <div>
        <div class="sense-main">
            <el-link icon="el-icon-arrow-left" type="primary" @click="goback">返回</el-link>
            <br>
            <br>
            <!-- view数据展示 -->
            <el-descriptions :column="4" border>
                <el-descriptions-item label="终端hostname">{{ hostname
                }}</el-descriptions-item>
                <el-descriptions-item label="所属公司">{{ companyname
                }}</el-descriptions-item>
                <el-descriptions-item label="机器guid">{{ guid
                }}</el-descriptions-item>
                <el-descriptions-item label="软件名称">{{ appname
                }}</el-descriptions-item>
                <el-descriptions-item label="上次存盘状态">
                    <span v-if="piracytypestr == ''">空</span>
                    <span v-else>{{ piracytypestr }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="判定依据">
                    <span v-if="piracyresult == ''">空</span>
                    <span v-else>{{ piracyresult }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="上次存盘时间">
                    <span v-if="lastverifytime == ''">空</span>
                    <span v-else>{{ lastverifytime }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="操作人">
                    <span v-if="lastverifyuser == ''">空</span>
                    <span v-else>{{ lastverifyuser }}</span>
                </el-descriptions-item>
            </el-descriptions>
            <br>
            <!-- 搜索 -->
            <span>起始时间 : </span>
            <el-date-picker v-model="startTime" size="small" type="datetime" placeholder="选择起始时间"
                :picker-options="pickerOptions" />
            <span style="margin-left: 1%;">结束时间 : </span>
            <el-date-picker v-model="endTime" size="small" type="datetime" placeholder="选择结束时间"
                :picker-options="pickerOptions" />

            <span style="margin-left: 1%;">复核状态 : </span>
            <el-select v-model="verifytype" size="small" placeholder="请选择">
                <el-option v-for="item in verifytypes" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <span style="margin-left: 1%;">复核结果 : </span>
            <el-select v-model="verifyresult" size="small" placeholder="请选择">
                <el-option v-for="item in verifyresults" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <span style="margin-left: 1%;">文件Hash检测状态 : </span>
            <el-select v-model="hashVerifyStatus" size="small" multiple collapse-tags placeholder="请选择">
                <el-option v-for="item in hashVerifyStatusOptions" :key="item.value" :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
            <el-button style="margin-left: 1%;" size="small" round type="primary" icon="el-icon-search"
                @click="GetTaskinfo">搜索</el-button>
            <br>
            <br>
            <div> <!-- 一键复核 -->
                &nbsp;&nbsp;<el-button type="primary" size="small" @click="VerifyTasks" round>一键复核</el-button>
                &nbsp;&nbsp;
                <!-- 复核存盘 -->
                <el-button type="primary" size="small" round @click="verifyTask">复核存盘</el-button>
            </div>

            <el-dialog v-draggable :visible.sync="dialogVisible" width="40%" ref="dialog" @close="verifydialogClose">
                <el-select clearable v-model="verifyval" style="margin-left: 25%;" placeholder="请选择">
                    <el-option v-for="item in verifyoptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button type="primary" style="margin-left: 10%;" round @click="getpinfo">智能提示</el-button>
                <br>
                <br>
                <div style="margin-left: 25%;">
                    <p>请选择盗版判定依据:</p><br>
                    <el-checkbox v-model="checked1" style="margin-bottom: 2%;" label="黑名单锁号" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasblacklock">存在黑名单锁号</i>
                    <br><el-checkbox v-model="checked3" style="margin-bottom: 2%;" label="黑名单机械" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasguidblack">此机械在黑名单中</i>
                    <br><el-checkbox v-model="checked4" style="margin-bottom: 2%;" label="黑名单模块" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasahxidblack">存在黑名单模块</i>
                    <br><el-checkbox v-model="checked5" style="margin-bottom: 2%;" label="服务器探针校验失败"
                        border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasplock">存在探针校验失败记录</i>
                    <br><el-checkbox v-model="checked6" style="margin-bottom: 2%;" label="非法一锁多机" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasmanyguid">检测到一锁多机,可能非法租赁</i>
                    <br><el-checkbox v-model="checked7" style="margin-bottom: 2%;" label="非法一机多锁" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hasmanylocks">检测到一机多锁,可能存在串货</i>
                    <br><el-checkbox v-model="checked8" style="margin-bottom: 2%;" label="文件Hash异常" border></el-checkbox>
                    <i class="el-icon-warning" v-if="hashashexception">检测到文件Hash异常</i>
                    <br>
                </div>
                <br>
                <el-input type="textarea" style="margin-left: 23%; width: 50%;margin-top: 1%;"
                    :autosize="{ minRows: 2 }" v-if="verifyval == 3" :rows="2" placeholder="请输入其他盗版判定依据"
                    v-model="otherstext">
                </el-input>
                <!-- 提交 -->
                <div style="margin-left: 60%;margin-bottom: 0%;">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" style="margin-left: 5%;" icon="el-icon-search"
                        @click="verifyTaskSave">存盘</el-button>
                </div>
            </el-dialog>
            <br>
            <br>
            <!-- 添加加载动画 -->
            <div v-if="loading" class="loading-container">
                <i class="el-icon-loading"></i>
                <p>数据加载中...</p>
            </div>
            <!-- 表格内容 -->
            <el-table v-else :data="taskdatas" height="550" border @selection-change="handleSelectionChange"
                style="width: 100%">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <!-- 上次复核结果 -->
                <el-table-column align="center" fixed header-align="center" label="上次复核结果" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.loading">
                            <!-- 加载中...... -->
                            <i class="el-icon-loading"></i>
                        </span>
                        <span v-else-if="!scope.row.piracystatus" class="sense-mestype sense-mestype-3">未复核</span>
                        <span v-else-if="scope.row.piracystatus === '盗版锁'" class="sense-mestype sense-mestype-2">{{
                            scope.row.piracystatus }}</span>
                        <span v-else-if="scope.row.piracystatus === '正版锁'" class="sense-mestype sense-mestype-5">{{
                            scope.row.piracystatus }}</span>
                        <span v-else class="sense-mestype sense-mestype-4">{{ scope.row.piracystatus }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed header-align="center" label="文件Hash检测状态" width="180">
                    <template slot-scope="scope">
                        <span v-if="scope.row.loading">
                            <!-- 加载中...... -->
                            <i class="el-icon-loading"></i>
                        </span>
                        <span v-if="scope.row.hashverifycode === 0" class="sense-mestype sense-mestype-5"></span> <!-- 成功 -->
                        <span v-else-if="scope.row.hashverifycode === 1" class="sense-mestype sense-mestype-4"></span> <!-- 未检测 -->
                        <span v-else-if="scope.row.hashverifycode === 2" class="sense-mestype sense-mestype-2"></span><!-- Hash验证失败 -->
                        <span v-else-if="scope.row.hashverifycode === 3" class="sense-mestype sense-mestype-1"></span><!-- 当前版本Hash未存储或未启用 -->
                        <span v-else-if="scope.row.hashverifycode === 4" class="sense-mestype sense-mestype-2"></span><!-- 缺失指定PeHash -->
                        <span v-else-if="scope.row.hashverifycode === 5" class="sense-mestype sense-mestype-1"></span><!-- 缺失PEinfo -->
                        <span v-else-if="scope.row.hashverifycode === 6" class="sense-mestype sense-mestype-1"></span><!-- 存在文件hash未上传 -->
                        <span v-else class="sense-mestype sense-mestype-5"></span>
                        {{ scope.row.hashverifyres }}
                    </template>
                </el-table-column>
                <!-- taskid -->
                <el-table-column prop="taskId" align="center" header-align="center" label="唯一ID" width="190">
                </el-table-column>
                <!-- appinfo -->
                <el-table-column prop="aadinfo.appname" align="center" header-align="center" label="APP信息" width="120">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="getAadinfo(scope.row.taskId)">
                            {{ scope.row.aadinfo.appname }}
                        </el-link>
                        <el-dialog title="软件信息" :modal="false" width="30%" top="10%" :visible.sync="appinfovisible">
                            <!-- 添加加载动画 -->
                            <div v-if="appLoading" class="loading-container">
                                <i class="el-icon-loading"></i>
                                <p>软件信息加载中...</p>
                            </div>
                            <div v-else-if="appinfo">
                                <el-descriptions :column="1" border>
                                    <el-descriptions-item label="软件id">{{ appinfo.aadinfo.appid
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="软件名称">{{ appinfo.aadinfo.appname
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="用户名称">{{ appinfo.aadinfo.hostname
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="软件版本信息">{{ appinfo.aadinfo.apmmoduleinfo
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="软件路径">{{ appinfo.aadinfo.appinstallpath
                                    }}</el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <!-- 提交 -->
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="appinfovisible = false">确定</el-button>
                            </div>
                        </el-dialog>
                    </template>
                </el-table-column>
                <!-- 锁信息 -->
                <el-table-column prop="lockinfo" align="center" header-align="center" label="锁信息" width="190">
                    <template v-slot="scope">
                        <span v-if="!scope.row.lockinfo">空</span>
                        <span v-else>{{ scope.row.lockinfo }}</span>
                    </template>
                </el-table-column>
                <!-- ip -->
                <el-table-column align="center" header-align="center" label="IP定位" width="210">
                    <template v-slot="scope">
                        <el-link type="primary" @click="markipshow(scope.row.ip)">
                            {{ scope.row.ip }}
                        </el-link>
                        <el-dialog title="标记此IP" :modal="false" width="40%" top="15%" :visible.sync="markipisvisible"
                            @close="resetSelector">
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="IP定位">{{ curip }}</el-descriptions-item>
                                <el-descriptions-item label="当前公司定位">{{ curipCompany || '未标记' }}</el-descriptions-item>
                            </el-descriptions>
                            <br>
                            <span>定位结果 : </span>
                            <el-select v-model="selectcompanyid" filterable clearable collapse-tags placeholder="请选择">
                                <el-option v-for="item in companynameoptions" :key="item.id" :label="item.companyname"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="markipisvisible = false">取消</el-button>
                                <el-button type="primary" style="margin-left: 5%;" icon="el-icon-search"
                                    @click="markip(curip)">标记</el-button>
                            </div>
                        </el-dialog>
                    </template>
                </el-table-column>
                <!-- wifi -->
                <el-table-column align="center" header-align="center" label="Wifi路由" width="190">
                    <template v-slot="scope">
                        <el-link type="primary" @click="markwifishow(scope.row.aadinfo.lanroutermac)">
                            {{ scope.row.aadinfo.lanroutermac }}
                        </el-link>
                        <el-dialog title="标记此Wifi" :modal="false" width="40%" top="15%" @close="resetSelector"
                            :visible.sync="markwifiisvisible">
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="Wifi定位">{{ curwifi }}</el-descriptions-item>
                                <el-descriptions-item label="当前公司定位">{{ curwifiCompany || '未标记'
                                    }}</el-descriptions-item>
                            </el-descriptions>
                            <br>
                            <span>定位结果 : </span>
                            <el-select v-model="selectcompanyid" filterable clearable collapse-tags @close=""
                                placeholder="请选择">
                                <el-option v-for="item in companynameoptions" :key="item.id" :label="item.companyname"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="markwifiisvisible = false">取消</el-button>
                                <el-button type="primary" style="margin-left: 5%;" icon="el-icon-search"
                                    @click="markwifi(curwifi)">标记</el-button>
                            </div>
                        </el-dialog>
                    </template>
                </el-table-column>
                <!-- 黑名单 -->
                <el-table-column prop="blacklistres" align="center" header-align="center" label="黑名单扫描" width="180">
                    <template v-slot="scope">
                        <span v-if="scope.row.blacklistres === ''">空</span>
                        <span v-else>{{ scope.row.blacklistres }}</span>
                    </template>
                </el-table-column>
                <!-- yara -->
                <el-table-column align="center" header-align="center" label="反黑模块扫描" width="160">
                    <template v-slot="scope">
                        <span v-if="!scope.row.ahxres">空</span>
                        <span v-else>{{ scope.row.ahxres }}</span>
                    </template>
                </el-table-column>
                <!-- peinfo -->
                <el-table-column align="center" header-align="center" label="PE信息" width="130">
                    <template v-slot="scope">
                        <el-link type="primary" @click="getPeinfo(scope.row.taskId)">
                            点击查看
                        </el-link>
                        <el-dialog title="PE信息" :modal="false" width="80%" top="10%" :visible.sync="peinfovisible">
                            <!-- 添加加载动画 -->
                            <div v-if="peLoading" class="loading-container">
                                <i class="el-icon-loading"></i>
                                <p>PE信息加载中...</p>
                            </div>
                            <el-table v-else :data="peinfo" border style="width: 100%" max-height="500">
                                <el-table-column prop="modulename" label="模块名称" width="180">
                                </el-table-column>
                                <el-table-column label="所属公司" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.companyname === ''">未知</span>
                                        <span v-else>{{ scope.row.companyname }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="文件描述" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.filedescription === ''">空</span>
                                        <span v-else>{{ scope.row.filedescription }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fileversion" label="文件版本" width="180">
                                </el-table-column>
                                <el-table-column prop="internalname" label="内部名称" width="180">
                                </el-table-column>
                                <el-table-column label="版权信息" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.legalcopyright === ''">空</span>
                                        <span v-else>{{ scope.row.legalcopyright }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商标信息" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.legaltrademarks === ''">空</span>
                                        <span v-else>{{ scope.row.legaltrademarks }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="originalfilename" label="原文件名" width="180">
                                </el-table-column>
                                <el-table-column label="privatebuild" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.privatebuild === ''">空</span>
                                        <span v-else>{{ scope.row.privatebuild }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productname" label="产品名称" width="180">
                                </el-table-column>
                                <el-table-column label="specialbuild" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.specialbuild === ''">空</span>
                                        <span v-else>{{ scope.row.specialbuild }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="filesize" label="文件大小(kb)" width="180">
                                </el-table-column>
                                <el-table-column label="comments" width="180">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.comments === ''">空</span>
                                        <span v-else>{{ scope.row.comments }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="sha256" label="文件Hash" width="560">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.sha256 === ''">空</span>
                                        <span v-else>{{ scope.row.sha256 }}</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="peinfovisible = false">确定</el-button>
                            </div>
                        </el-dialog>
                    </template>
                </el-table-column>
                <!-- moduleinfo -->
                <el-table-column align="center" header-align="center" label="Module信息" width="130">
                    <template v-slot="scope">
                        <el-link type="primary" @click="getModuleinfo(scope.row.taskId)">
                            点击查看
                        </el-link>
                        <el-dialog title="Module信息" :modal="false" width="70%" top="10%"
                            :visible.sync="moduleinfovisible">
                            <!-- 添加加载动画 -->
                            <div v-if="moduleLoading" class="loading-container">
                                <i class="el-icon-loading"></i>
                                <p>Module信息加载中...</p>
                            </div>
                            <el-table v-else :data="moduleinfo" border style="width: 100%" max-height="500">
                                <el-table-column prop="szmodule" label="szmodule" width="180">
                                </el-table-column>
                                <el-table-column prop="szexepath" label="szexepath" width="180">
                                </el-table-column>
                                <el-table-column prop="hmoduleval" label="hmoduleval" width="180">
                                </el-table-column>
                                <el-table-column prop="modbasesize" label="modbasesize" width="180">
                                </el-table-column>
                                <el-table-column prop="modbasebyte" label="modbasebyte" width="180">
                                </el-table-column>
                                <el-table-column prop="proccntusage" label="proccntusage" width="180">
                                </el-table-column>
                                <el-table-column prop="glblcntusage" label="glblcntusage" width="180">
                                </el-table-column>
                                <el-table-column prop="th32processid" label="th32processid" width="180">
                                </el-table-column>
                                <el-table-column prop="th32moduleid" label="th32moduleid" width="180">
                                </el-table-column>
                            </el-table>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="moduleinfovisible = false">确定</el-button>
                            </div>
                        </el-dialog>
                    </template>
                </el-table-column>
                <!-- license -->
                <el-table-column prop="licenseres" align="center" header-align="center" label="许可信息" width="180">
                </el-table-column>
                <!-- time -->
                <el-table-column prop="time" align="center" header-align="center" label="上传时间" width="180">
                </el-table-column>
            </el-table>
        </div>
        <div class="sense-table-pager" v-if="total > 0">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="currentPage" :page-sizes="[10, 20, 30]" :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div>
    </div>

</template>

<style>
.el-dialog__wrapper {
    pointer-events: none;
}

.el-dialog {
    pointer-events: auto;
    margin: 0 !important;
    top: 150px;
    left: 50%;
    transform: translate(-50%, 0);
}

.v-modal {
    display: none;
}

/* 加载动画样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
}

.loading-container i {
    font-size: 40px;
    color: #409EFF;
    margin-bottom: 15px;
}

.loading-container p {
    font-size: 16px;
    color: #606266;
}
</style>

<script>
import apiClient from '../api/axios';

export default {
    data() {
        return {
            loading: true, // 添加加载状态变量
            peLoading: false, // PE信息加载状态
            moduleLoading: false, // Module信息加载状态
            appLoading: false, // App信息加载状态
            curip: '',
            curwifi: '',
            curipCompany: '', // 当前IP对应的公司名
            curwifiCompany: '', // 当前WiFi对应的公司名

            // 查询
            startTime: '',
            endTime: '',
            verifytype: -1,
            pickerOptions: {
                disabledDate(time) {
                    // 禁止选择未来时间
                    return time.getTime() > Date.now();
                }
            },
            verifytypes: [
                {
                    value: -1,
                    label: '全部',
                },
                {
                    value: 1,
                    label: '未复核',
                },
                {
                    value: 255,
                    label: '已复核',
                }
            ],
            verifyresult: -1,
            verifyresults: [
                {
                    value: -1,
                    label: '全部',
                },
                {
                    value: 1,
                    label: '正版锁',
                },
                {
                    value: 2,
                    label: '盗版锁',
                },
                {
                    value: 3,
                    label: '数据不完整',
                },
            ],

            hashVerifyStatus: [],
            hashVerifyStatusOptions: [
                { value: 0, label: '成功' },
                { value: 1, label: '未检测' },
                { value: 2, label: 'Hash验证失败' },
                { value: 3, label: '当前版本Hash未存储或未启用' },
                { value: 4, label: '缺少指定PeHash' },
                { value: 5, label: '缺失PEinfo' },
                { value: 6, label: '存在文件hash未上传' },
            ],

            checked1: false,
            checked3: false,
            checked4: false,
            checked5: false,
            checked6: false,
            checked7: false,
            checked8: false,
            hasplock: false, // 是否有盗版锁提示
            hasmanyguid: false, // 一锁多机提示
            hasblacklock: false, // 黑名单锁号提示
            hasmanylocks: false, // 一机多锁提示
            hasahxidblack: false, // 反黑id黑名单提示
            hasguidblack: false, // 机器guid黑名单提示
            hashashexception: false, // 文件Hash异常提示
            showModal: false,
            dialogVisible: false,

            // 上次存盘记录信息
            piracytypestr: '', // 盗版类型
            piracyresult: '', // 盗版判定依据
            lastverifytime: '', // 上次存盘时间
            lastverifyuser: '', // 上次存盘用户


            taskdatas: [], // task数据
            verifyval: '', // 复核结果
            markipisvisible: false, // 标记ip弹窗
            markwifiisvisible: false, // 标记ip弹窗
            companynameoptions: [],
            otherstext: '', // 其他盗版判定依据
            selectcompanyid: '',
            verifyoptions: [
                {
                    value: '-1',
                    label: '正盗混用',
                },
                {
                    value: '-2',
                    label: '明确盗版',
                },
                {
                    value: '-3',
                    label: '滥用授权',
                },
                {
                    value: '-4',
                    label: '回收正版锁',
                },
                {
                    value: '-5',
                    label: '偷盗授权',
                },
                {
                    value: '-7',
                    label: '黑名单',
                },
                {
                    value: '1',
                    label: '异常需人工排查',
                },
                {
                    value: '2',
                    label: '试用/演示/学习版本',
                },
                {
                    value: '3',
                    label: '其他意见',
                },
                {
                    value: '255',
                    label: '明确正版',
                },
            ], // 复核选项
            pcheckList: [], // 盗版信息选择
            viewdataid: '',
            hostname: '',
            guid: '',
            companyname: '',
            appname: '',
            currentPage: 1,  //当前页
            pageSize: 10,  //每页条数
            total: 0,  //数据总数
            appinfo: null, // app信息
            peinfo: null, // pe信息
            moduleinfo: null, // module信息
            multipleSelection: [], // 多选
            appinfovisible: false, // app信息弹窗
            peinfovisible: false, // pe信息弹窗
            moduleinfovisible: false, // module信息弹窗
            verifyisvisiable: false, // 复核弹窗
        }
    },
    // 获取viewdataid
    mounted() {
        this.viewdataid = this.$store.state.Viewdataid.viewdataid;
        this.hostname = this.$store.state.TaskInfo.hostname;
        this.guid = this.$store.state.TaskInfo.guid;
        this.companyname = this.$store.state.TaskInfo.companyname;
        this.appname = this.$store.state.TaskInfo.appname;
        this.GetTaskinfo();
        this.getLastReviewInfo();
    },
    methods: {
        showDialog() {
            this.dialogVisible = true;
        },
        closeModal() {
            this.showModal = false;
        },
        startDrag(e) {
            // 记录鼠标位置和弹窗的初始位置
            this.isDragging = true;
            this.offsetX = e.clientX - this.modalX;
            this.offsetY = e.clientY - this.modalY;
            document.addEventListener('mousemove', this.onDrag);
            document.addEventListener('mouseup', this.stopDrag);
        },
        onDrag(e) {
            if (this.isDragging) {
                this.modalX = e.clientX - this.offsetX;
                this.modalY = e.clientY - this.offsetY;
                this.$refs.modal.style.left = this.modalX + 'px';
                this.$refs.modal.style.top = this.modalY + 'px';
            }
        },
        stopDrag() {
            this.isDragging = false;
            document.removeEventListener('mousemove', this.onDrag);
            document.removeEventListener('mouseup', this.stopDrag);
        },

        goback() {
            this.$router.go(-1);
        },
        // dialog关闭时清空数据
        resetSelector() {
            this.selectcompanyid = '';
        },
        // 复核存盘
        verifyTask() {
            this.dialogVisible = true;
        },
        // 获取智能提示
        getpinfo() {
            let url = '/lic/getlastreviewinfo'
            let param = {
                'viewdataid': this.viewdataid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    // 正确的判断方式：使用逻辑与(&&)连接所有条件
                    if (!res.data.hasblacklock && !res.data.hasmanyguid && !res.data.hasmanylocks && 
                        !res.data.hasahxidblack && !res.data.hasguidblack && !res.data.hashashexception) {
                        this.$message({
                            message: '未发现异常',
                            type: 'success',
                            duration: 2000,
                        });
                        return
                    }
                    this.$message({
                        message: '获取成功',
                        type: 'success',
                        duration: 2000,
                    });
                    // 复核失败记录
                    if (res.data.hasplock == true) {
                        this.hasplock = true;
                    }
                    // 黑名单锁号
                    if (res.data.hasblacklock == true) {
                        this.hasblacklock = true;
                    }
                    // 一锁多机
                    if (res.data.hasmanyguid == true) {
                        this.hasmanyguid = true;
                    }
                    // 一机多锁
                    if (res.data.hasmanylocks == true) {
                        this.hasmanylocks = true;
                    }
                    // 黑名单ahxid
                    if (res.data.hasahxidblack == true) {
                        this.hasahxidblack = true;
                    }
                    // 黑名单guid
                    if (res.data.hasguidblack == true) {
                        this.hasguidblack = true;
                    }
                    // 文件Hash异常
                    if (res.data.hashashexception == true) {
                        this.hashashexception = true;
                    }
                } else if (res.data.code == -1) {
                    this.$message.warning("请先进行数据复核");
                } else {
                    this.$message.error(res.data.msg);
                }
            })
        },

        // 关闭复核存盘的对话框
        verifydialogClose() {
            this.checked1 = false;
            this.checked2 = false;
            this.checked3 = false;
            this.checked4 = false;
            this.checked5 = false;
            this.checked6 = false;
            this.checked7 = false;
            this.checked8 = false;
            this.otherstext = '';
            this.hasplock = false;
            this.hasblacklock = false;
            this.hasmanyguid = false;
            this.hasmanylocks = false;
            this.hasahxidblack = false;
            this.hasguidblack = false;
            this.hashashexception = false;
            this.verifyval = '';
        },

        // 复核存盘
        verifyTaskSave() {
            if (this.checked1 == true) {
                this.pcheckList.push("存在黑名单锁号");
            }
            if (this.checked3 == true) {
                this.pcheckList.push("黑名单机械");
            }
            if (this.checked4 == true) {
                this.pcheckList.push("黑名单模块");
            }
            if (this.checked5 == true) {
                this.pcheckList.push("服务器探针校验失败");
            }
            if (this.checked6 == true) {
                this.pcheckList.push("非法一锁多机");
            }
            if (this.checked7 == true) {
                this.pcheckList.push("非法一机多锁");
            }
            if (this.checked8 == true) {
                this.pcheckList.push("文件Hash异常");
            }
            if (this.verifyval == '3') {
                if (this.otherstext != '') {
                    this.pcheckList.push(this.otherstext);
                }
            }
            let url = '/lic/savereview'
            let param = {
                'viewdataid': this.viewdataid,
                'rescode': this.verifyval,
                'msg': this.pcheckList.join(','),
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.$message({
                        message: '存盘成功',
                        type: 'success',
                        duration: 2000,
                    });
                } else {
                    this.$message.error("存盘失败:" + res.data.msg);
                }
            })
            this.pcheckList = [];
        },

        showtipmessages(val) {
            setTimeout(() => {
                this.$notify({
                    title: '异常',
                    message: val,
                    duration: 0,
                    offset: 100,
                });
            }, 0);

        },

        // 多选
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        //切换每页显示条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.GetTaskinfo();
        },
        //切换当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.GetTaskinfo();
        },

        // ip标记显示
        markipshow(ip) {
            this.markipisvisible = true;
            this.companynameisvisible = true;
            this.curip = ip;
            this.curipCompany = ''; // 重置当前IP对应的公司名
            this.selectcompanyid = ''; // 重置选择的公司ID
            
            // 获取当前IP对应的公司名
            apiClient.post('/getcompanynamebyip', { 'ip': ip.split('-')[0] })
                .then(response => {
                    if (response.data.code === 0 && response.data.data) {
                        this.curipCompany = response.data.data || '未标记';
                    }
                })
                .catch(error => {
                    this.$message({
                        message: error,
                        type: 'warning'
                    });
                });
            
            // 获取所有公司名称
            apiClient.get('/getallcompanynames').then(response => {
                this.companynameoptions = response.data.data;
            }).catch(error => {
                this.$message({
                    message: error,
                    type: 'warning'
                });
            });
        },
        // wifi标记显示
        markwifishow(wifi) {
            this.markwifiisvisible = true;
            this.companynameisvisible = true;
            this.curwifi = wifi;
            this.curwifiCompany = ''; // 重置当前WiFi对应的公司名
            this.selectcompanyid = ''; // 重置选择的公司ID
            
            // 获取当前WiFi对应的公司名
            apiClient.post('/getcompanynamebywifirouter', { 'wifirouter': wifi })
                .then(response => {
                    if (response.data.code === 0 && response.data.data) {
                        this.curwifiCompany = response.data.data || '未标记';
                    }
                })
                .catch(error => {
                    this.$message({
                        message: error,
                        type: 'warning'
                    });
                });
            
            // 获取所有公司名称
            apiClient.get('/getallcompanynames').then(response => {
                this.companynameoptions = response.data.data;
            }).catch(error => {
                this.$message({
                    message: error,
                    type: 'warning'
                });
            });
        },

        // 标记ip-公司
        markip(ip) {
            if (!this.selectcompanyid) {
                this.$message.warning('请先选择公司');
                return;
            }
            
            let url = '/markcompanyip'
            let param = {
                'ip': ip.split('-')[0],
                'companyid': this.selectcompanyid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.$message.success("标记成功");
                    // 更新当前显示的公司名
                    this.getIpCompany(ip.split('-')[0]);
                } else {
                    this.$message.error("标记失败:" + res.data.msg);
                }
            })
        },
        // 标记wifi-公司
        markwifi(wifirouter) {
            if (!this.selectcompanyid) {
                this.$message.warning('请先选择公司');
                return;
            }
            
            let url = '/markcompanywifirouter'
            let param = {
                'wifirouter': wifirouter,
                'companyid': this.selectcompanyid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.$message.success("标记成功");
                    // 更新当前显示的公司名
                    this.getWifiCompany(wifirouter);
                } else {
                    this.$message.error("标记失败:" + res.data.msg);
                }
            })
        },
        
        // 获取IP对应的公司名
        getIpCompany(ip) {
            apiClient.post('/getcompanynamebyip', { 'ip': ip })
                .then(response => {
                    if (response.data.code === 0 && response.data.data) {
                        this.curipCompany = response.data.data || '未标记';
                    }
                })
                .catch(error => {
                    this.$message({
                        message: error,
                        type: 'warning'
                    });
                });
        },
        
        // 获取WiFi对应的公司名
        getWifiCompany(wifi) {
            apiClient.post('/getcompanynamebywifirouter', { 'wifirouter': wifi })
                .then(response => {
                    if (response.data.code === 0 && response.data.data) {
                        this.curwifiCompany = response.data.data || '未标记';
                    }
                })
                .catch(error => {
                    this.$message({
                        message: error,
                        type: 'warning'
                    });
                });
        },

        // 获取aad信息
        getAadinfo(taskid) {
            this.appinfovisible = true;
            this.appLoading = true; // 设置加载状态为true
            this.appinfo = null; // 清空之前的数据
            let url = '/lic/getaadbytaskid'
            let param = {
                'taskid': taskid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.appinfo = res.data.data;
                } else {
                    this.$message.error(res.data.msg);
                }
                this.appLoading = false; // 在请求完成后设置加载状态为false
            }).catch((error) => {
                this.$message.error("获取APP信息失败:" + error);
                this.appLoading = false; // 确保在请求失败时也将加载状态设为false
            })
        },
        // 获取pe信息
        getPeinfo(taskid) {
            this.peinfovisible = true;
            this.peLoading = true; // 设置加载状态为true
            this.peinfo = null; // 清空之前的数据
            let url = '/lic/getpebytaskid'
            let param = {
                'taskid': taskid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.peinfo = res.data.data;
                } else {
                    this.$message.error(res.data.msg);
                }
                this.peLoading = false; // 在请求完成后设置加载状态为false
            }).catch((error) => {
                this.$message.error("获取PE信息失败:" + error);
                this.peLoading = false; // 确保在请求失败时也将加载状态设为false
            })
        },

        // 获取module信息
        getModuleinfo(taskid) {
            this.moduleinfovisible = true;
            this.moduleLoading = true; // 设置加载状态为true
            this.moduleinfo = null; // 清空之前的数据
            let url = '/lic/getmodulebytaskid'
            let param = {
                'taskid': taskid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.moduleinfo = res.data.data;
                } else {
                    this.$message.error(res.data.msg);
                }
                this.moduleLoading = false; // 在请求完成后设置加载状态为false
            }).catch((error) => {
                this.$message.error("获取Module信息失败:" + error);
                this.moduleLoading = false; // 确保在请求失败时也将加载状态设为false
            })
        },

        // 初始获取task信息
        GetTaskinfo() {
            this.loading = true; // 在请求前设置加载状态为true
            let url = '/lic/gettaskbypc'
            let param = {
                "viewdataid": this.viewdataid,
                "page": this.currentPage,
                "limit": this.pageSize,
                "taskstatus": this.verifytype,
                "piracystatus": this.verifyresult
            }
            if (this.startTime !== '') {
                param.starttime = this.startTime
            }
            if (this.endTime !== '') {
                param.endtime = this.endTime
            }
            if (this.hashVerifyStatus.length > 0) {
                param.hashverifycodes = this.hashVerifyStatus;
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    // this.total = res.data.data.count;
                    this.total = res.data.total;
                    if (res.data.total > 0) {
                        this.taskdatas = res.data.data;
                        this.taskdatas.forEach(row => row.loading = false);
                    } else {
                        this.taskdatas = [];
                    }
                    this.$message.success("查询成功")
                } else {
                    this.$message.error(res.data.msg);
                }
                this.loading = false; // 在请求完成后设置加载状态为false
            }).catch((error) => {
                this.$message.error("获取数据失败:"+error);
                this.loading = false; // 确保在请求失败时也将加载状态设为false
            })
        },

        // 获取上次存盘信息
        getLastReviewInfo() {
            let url = '/lic/getlastsavereview'
            let param = {
                'viewdataid': this.viewdataid,
            }
            apiClient.post(url, param).then((res) => {
                if (res.data.code == 0) {
                    this.piracytypestr = res.data.data == null ? '' : res.data.data.piracytypestr;
                    this.piracyresult = res.data.data == null ? '' : res.data.data.piracytyperemark;
                    this.lastverifytime = res.data.data == null ? '' : res.data.data.time;
                    this.lastverifyuser = res.data.data == null ? '' : res.data.data.username;
                } else {
                    this.$message.error(res.data.msg);
                }
            })
        },

        // 一键复核
        async VerifyTasks() {
            if (this.multipleSelection.length === 0) {
                this.$message.warning('请先选择要复核的数据');
                return;
            } else {
                this.$message.success('复核已发送');
            }
            this.multipleSelection = [...this.multipleSelection];
            try {
                const responses = await Promise.all(
                    this.multipleSelection.map((item) => {
                        return new Promise((resolve) => {
                            item.loading = true;
                            apiClient.post('/lic/reviewtask', { 'taskid': item.taskId, }).then(
                                (res) => {
                                    if (res.data.code === 0) {
                                        // 更新状态和加载标志
                                        this.$set(item, 'piracystatus', res.data.status);
                                        this.$set(item, 'loading', false); // 确保触发响应式更新
                                    } else {
                                        this.$message.error(res.data.msg);
                                        this.$set(item, 'loading', false); // 确保结束加载状态
                                        return;
                                    }
                                    resolve(item); // 继续处理 Promise
                                }
                            )
                        });
                    })
                );
                this.$message.success('复核完成');
                this.GetTaskinfo();
            } catch (error) {
                this.$message.error('复核失败');
            }
        },
    },
}
</script>
