const state = {
    hostname : "",
    guid : "",
    appname : "",
    companyname : "",
};

const mutations = {
    SET_Task_HOSTNAME: (state, msg) => {
        state.hostname = msg
    },
    SET_Task_GUID: (state, msg) => {
        state.guid = msg
    },
    SET_Task_APPNAME: (state, msg) => {
        state.appname = msg
    },
    SET_Task_COMPANYNAME: (state, msg) => {
        state.companyname = msg
    },
};

const getters = {}
const actions = {}
export default {
    state,
    getters,
    actions,
    mutations
}