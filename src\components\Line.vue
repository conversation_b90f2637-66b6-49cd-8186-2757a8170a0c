<template>
    <div class="ahs-line" id="ahs_line"></div>
</template>

<script>

export default {
    props: ['lineObj'],
    methods: {
        //折线图
        loadLine(date, value) {
            let line = this.$echarts.init(document.getElementById('ahs_line'));

            let option = {
                color: ['rgba(24, 144, 255, 0.85)'],
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        lineStyle: {
                            color: '#666'
                        }
                    },
                    data: date
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#666'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#e8e8e8',
                            type: 'dashed'
                        }
                    }
                },
                series: [{
                    data: value,
                    type: 'line',
                    areaStyle: {
                        normal: {
                            color: 'rgba(24, 144, 255, 0.3)'
                        }
                    },
                    smooth: true
                }]
            };
            line.setOption(option);

        },
    },
    mounted() {
        //this.loadLine();
    },
}
</script>

<style lang="scss">
.ahs-line {
    width: 820px;
    height: 340px;
}
</style>
