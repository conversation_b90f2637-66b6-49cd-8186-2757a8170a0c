$common-color: #409eff;
$common-hover-color: #66b1ff;

html, body, button, input, select, textarea, p, ul, ol, h1, h2, h3, h4, h5, h6, a {
	margin: 0px;
	padding: 0px;
	font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;
	font-size: 14px;
	color: #353535;
	-webkit-font-smoothing: antialiased;
}

// 取消router-link默认的高亮样式
.router-link-active, 
.router-link-exact-active {
  color: inherit !important; /* 继承父元素颜色，避免默认高亮 */
  text-decoration: none; /* 可选：去掉下划线 */
}


html, body {
	width: 100%;
	height: 100%;
	background: #f0f2f5;
}
h1, h2, h3, h4, h5, h6 {
	font-weight: normal;
}
img, a img {
	vertical-align: top;
	border: 0;
}
ul, li {
	list-style: none;
}

.text-right {
	text-align: right;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.fl {
	float: left;
}
.fr {
	float: right;
}
.posi-relative {
	position: relative;
}

a,
a:hover,
a:focus {
	outline: none;
	text-decoration: none;
}

.clearfix:after {
	display: block;
	content: "";
	height: 0;
	overflow: hidden;
	clear: both;
}
.clearfix {
	zoom: 1;
}

.sense-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: block;
}

.sense-container {
	display: flex;
    flex: auto;
    flex-direction: column;
    min-height: 100vh;
}

.sense-footer, .sense-header {
    flex: 0 0 auto;
}

.sense-content {
	flex: auto;
}

.sense-footer {
	text-align: center;
	padding: 30px 0;
	text-align: center;
	color: rgba(0,0,0,.45);
}

.sense-footer a {
	color: rgba(0,0,0,.45);
}
.sense-footer a:hover {
  color: $common-color;
}

.sense-container {
	width: 100%;
}

.sense-main {
	background: #fff;
	padding: 24px;
}

.sense-header {
    background: #00479d;
    height: 64px;
    line-height: 64px;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    transition: background .3s,width .2s;
}

.sense-header h1 {
	color: #fff;
	padding: 0 14px 0 36px;
	font-size: 18px;
	color: #35aeda;
	// background: url(../img/apm.ico) no-repeat left center / 30px 30px;
}

.dev-name {
	float: right;
	color: hsla(0,0%,100%,.85);
	max-width: 180px;
	height: 64px;
}

.dev-logo {
	border-radius: 100%;
	float: right;
	margin: 15px 10px 0 0;
	width: 32px;
	height: 32px;
}

.sense-header .sense-dropdown .el-dropdown {
	float: right;
	max-width: 260px;
	padding: 0 15px;
	transition: all .3s;
	&:hover {
		background: #1890ff;
	}
}

.el-dropdown-menu__item {
	font-size: 13px;
	padding: 0 25px 0 20px;
}

.el-dropdown-menu li i {
	font-size: 15px;
	width: 16px;
	margin-right: 5px;
	text-align: center;
}

.sense-header .el-dropdown-link {
	cursor: pointer;
}

.el-dropdown-menu-notice {
	padding: 0;
	width: 300px;
}
.el-dropdown-menu-upgrade {
	width: 336px;
}
.el-dropdown-menu-notice .el-dropdown-menu__item {
	padding: 0;
	&:hover, &:focus {
		background-color: #fff;
    	color: #606266;
	}
}
.el-dropdown-menu .el-tabs__header {
	margin: 0;
}
.sense-notice {
	position: relative;
	cursor: pointer;
}

.sense-notice .sense-notice-icon {
	display: block;
	padding: 0 15px;
	height: 64px;
	transition: all .3s;
	&:hover {
		background: #1890ff;
	}
	&:focus {
		outline: none;
	}
}
.sense-notice .sense-notice-icon.active {
	background: #1890ff;
}

.sense-notice .sense-notice-icon i {
	font-size: 16px;
	color: hsla(0,0%,100%,.85);
}

.notice-con,
.upgrade-con {
	min-height: 207px;
	max-height: 345px;
	overflow-y: auto;
}

.sense-tabs-list {
	padding: 12px 24px;
	border-bottom: 1px solid #e8e8e8;
}
.sense-tabs-list .sense-tabs-icon {
	width: 32px;
	height: 32px;
    line-height: 32px;
	border-radius: 100%;
	text-align: center;
	color: #fff;
	margin: 5px 16px 0 0;
}
.sense-tabs-list .sense-tabs-icon-blue {
	background: $common-color;
}
.sense-tabs-list .sense-tabs-icon-red {
	background: #fe5d58;
}
.sense-tabs-list .sense-tabs-icon-yellow {
	background: #ffce3d;
}
.sense-tabs-list .sense-tabs-text{
	max-width: 200px;
}
.sense-tabs-list .sense-tabs-text h3 {
	color: rgba(0,0,0,.65);
	line-height: 24px;
}
.sense-tabs-list .sense-tabs-text p {
	color: rgba(0,0,0,.45);
	line-height: 20px;
	font-size: 12px;
}
.sense-tabs-list .sense-tabs-operate a {
	line-height: 44px;
	color: $common-color;
	&:hover {
		color: $common-hover-color;
	}
}

.el-tabs--border-card {
	-webkit-box-shadow: 0 0 8px 0 rgba(0,0,0,.1);
    box-shadow: 0 0 8px 0 rgba(0,0,0,.1);
}
.el-tabs--border-card>.el-tabs__content {
	padding: 24px;
}
.el-tabs--border-card .el-tabs__item {
	font-size: 15px;
	height: 48px;
	line-height: 48px;
}

.sense-header-box {
	width: 1340px;
	margin: 0 auto;
}

.sense-content {
	width: 100%;
	margin: 24px auto;
}

.sense-nav .el-menu-item {
	padding: 0;
}

.sense-nav .el-menu-item .tag-list {
	display: block;
	padding: 0 30px;
	color: #fff;
}

.el-menu.el-menu--horizontal {
	border: 0;
	border-bottom: none !important;
}

.el-menu--horizontal>.el-menu-item {
	border: 0;
	height: 64px;
	line-height: 64px;
}


.el-tooltip__popper {
	padding: 9px 10px;
}

.sense-slot {
	line-height: 24px;
}

.sense-function-btn {
	padding-bottom: 24px;
}

.el-button--small {
	padding: 10px 15px;
	font-size: 14px;
}

/*login*/
.login-body {
	display: flex;
	flex-direction: column;
	height: 100vh;
	overflow: auto;
	background-color: #f0f2f5;
}
.login-main {
	flex: 1 1;
}
.login {
	width: 388px;
	margin: 0 auto;
	border-radius: 5px;
	h1 {
		text-align: center;
		padding: 120px 0 70px 0;
	}
	.login-button {
		width: 100%;
	}
	.el-form-item {
		margin-bottom: 30px;
	}
	.el-input__inner {
		height: 40px;
		line-height: 40px;
		padding-left: 36px;
	}
	.el-button {
	    padding: 12px 20px;
	}
	.login-icon {
		position: absolute;
	    left: 13px;
	    top: 0;
	    font-size: 15px;
	    color: #c0c4cc;
	}
	.fa-envelope-o {
	    font-size: 13px;
    	top: 13px;
	}
	.img-captcha {
		position: absolute;
		right: 14px;
		top: 5px;
	}
}
.login .input-captcha .el-input__inner {
	padding-right: 110px;
}
.login h1 span {
	font-size: 32px;
	font-weight: bold;
	color: rgba(0, 0, 0, 0.85);
	margin-left: 10px;
}
.login-link a {
	color: $common-color;
	&:hover {
		color: $common-hover-color;
	}
}

/*概览*/
/*card*/
.sense-card {
	margin: 0 -12px 24px -12px;
}

.sense-card .el-col {
	padding: 0 12px;
}

.sense-card .sense-card-main {
	background: #fff;
	padding: 20px 25px;
	position: relative;
}

.sense-card .sense-card-main .sense-card-title {
	color: rgba(0,0,0,.45);
}

.sense-card .sense-card-main .sense-card-num {
	color: rgba(0,0,0,.85);
	font-size: 28px;
    padding-top: 6px;
}

.sense-card .sense-card-main .sense-card-info {
	position: absolute;
	right: 25px;
	top: 21px;
	font-size: 16px;
	cursor: pointer;
	color: rgba(0, 0, 0, 0.35);
}

/*chart*/
.sense-chart {
	background: #fff;
	margin-bottom: 24px;
	position: relative;
}

/*tabs*/
.sense-tabs .el-tabs__nav-wrap::after {
	height: 1px;
	background-color: #e8e8e8;
}
.sense-tabs .el-tabs__item {
	height: 45px;
	line-height: 45px;
}
.sense-tabs .el-tabs__nav-scroll {
	padding-left: 24px;
}

.sense-chart .sense-chart-title {
	position: absolute;
	left: 38px;
    top: 10px;
	font-size: 16px;
	font-weight: bold;
}

/*trend*/
.sense-trend .sense-trend-title {
	height: 54px;
	line-height: 54px;
	margin-bottom: 15px;
	border-bottom: 1px solid #e8e8e8;
	font-size: 16px;
	padding-left: 16px;
}
.sense-trend .sense-trend-title a {
	padding: 0 16px;
	height: 54px;
	box-sizing: border-box;
	position: relative;
	top: 1px;
	margin-right: 16px;
	transition: all .3s;
}
.sense-trend .sense-trend-title a.active {
	border-bottom: 2px solid $common-color;
	color: $common-color;
}

/*extra*/
.sense-extra {
	position: absolute;
    right: 24px;
    top: 17px;
}
.sense-extra a,
.sense-extra1 a {
	color: rgba(0,0,0,.65);
	margin-right: 12px;
	&:last-child {
		margin-right: 0;
	}
}
.sense-extra a.active,
.sense-extra1 a.active {
	color: rgba(24, 144, 255, 0.85);
}

/*map*/
.sense-map {
	background: #fff;
	margin-bottom: 24px;
}
.sense-map .sense-map-title {
	height: 54px;
	line-height: 54px;
	font-size: 16px;
	border-bottom: 1px solid #e8e8e8;
	padding: 0 24px;
}
.sense-screen {
	padding: 20px;
}
.sense-radio-group .el-radio-button--small .el-radio-button__inner {
	padding: 9px 20px;
}
.sense-radio-group .el-radio-button__orig-radio:checked+.el-radio-button__inner {
	color: $common-color;
    background-color: #fff;
}
.sense-china-map {
	width: 820px;
	height: 580px;
	overflow: hidden;
	position: relative;
}
.sense-china-map .map-conp {
	position: absolute;
    left: -20px;
    top: -160px;
}

/*ranking-list*/
.sense-top10 {
	width: 380px;
}
.sense-top10 h3 {
	color: rgba(0, 0, 0, 0.85);
	font-size: 16px;
	font-weight: bold;
	padding: 25px 0 15px 0;
}
.sense-ranking-list li {
	margin-top: 20px;
	height: 20px;
	line-height: 20px;
}
.sense-ranking-list li .sense-ranking-list-num {
	width: 20px;
    height: 20px;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 20px;
}
.sense-ranking-list li.active .sense-ranking-list-num {
	color: #fff;
    background-color: #314659;
}
.sense-ranking-list li .sense-ranking-list-name {
	padding: 0 24px;
	width: 190px;
}

/*table*/
.sense-table-list {
	min-height: 616px;
}
.sense-table-list .el-table th {
	background: #fafafa;
	font-weight: normal;
    color: rgba(0,0,0,.85);
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 0;
}
.sense-table-list .el-table--border th:nth-last-child(2) {
	border-right: 0;
}
.sense-table-list .el-table td {
	color: rgba(0,0,0,.65);
	padding: 16px 0;
}
.sense-table-list .el-table--border::after {
	width: 0px;
}
.sense-table-list .el-table--enable-row-hover .el-table__body tr:hover>td {
	background: #e6f7ff;
}

.sense-table-ellipsis .el-table td .cell {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.sense-mestype:before {
	content: "";
	float: left;
	width: 10px;
	height: 10px;
	margin: 6px 10px 0 0;
	border-radius: 100%;
}
.sense-mestype-0:before {
	background: #ccc;
}
.sense-mestype-1:before {
	background: #840b12;
}
.sense-mestype-2:before {
	background: #f5222d;
}
.sense-mestype-3:before {
	background: #f39d1c;
}
.sense-mestype-4:before {
	background: #F5E322;
}
.sense-mestype-5:before {
	background: $common-color;
}
.table-link {
	color: $common-color;
	cursor: pointer;
	&:hover {
		color: $common-hover-color;
	}
}
.sense-table-list .operate-icon {
	font-size: 16px;
	color: $common-color;
	cursor: pointer;
	margin-right: 5px;
	&:hover {
		color: $common-hover-color;
	}
}
.sense-table-list .operate-text {
	color: $common-color;
	&:hover {
		color: $common-hover-color;
	}
}
.soft-logo {
	width: 32px;
	height: 32px;
	border-radius: 100%;
}
.machineId-copy-val {
	max-width: 160px;
	float: left;
}
.machineId-copy {
	display: none;
	float: right;
	margin-right: 12px;
	color: $common-color;
	&:hover {
		color: $common-hover-color;
	}
}
.machineId-copy-box td:hover .machineId-copy {
	display: block;
}

/*table-pager*/
.sense-table-pager {
	margin-top: 24px;
	text-align: right;
}
.sense-table-pager .el-pager li {
	font-weight: normal;
}

/*breadcrumb*/
.sense-breadcrumb .el-breadcrumb {
	margin-bottom: 24px;
}
.sense-breadcrumb .el-breadcrumb__inner a, .el-breadcrumb__inner.is-link {
	color: $common-color;
	font-weight: normal;
	&:hover {
		color: $common-hover-color;
	}
}

/*dialog*/
.sense-dialog .el-dialog {
	width: 540px;
}
.sense-dialog .el-input__inner,
.sense-dialog .el-textarea__inner,
.sense-dialog .el-select {
	width: 90%;
}
.sense-dialog .el-select .el-input__inner {
	width: 100%;
}
.sense-dialog .el-dialog__header {
	padding: 16px 24px;
	border-bottom: 1px solid #e8e8e8;
}
.sense-dialog .el-dialog__title {
	font-size: 16px;
}
.sense-dialog .el-dialog__headerbtn {
	top: 17px;
}
.sense-dialog .sense-text {
	color: #999;
	word-break: break-all;
}
.sense-dialog .sense-tip {
	color: #999;
	font-size: 12px;
	line-height: 20px;
}
.sense-dialog .sense-tip-link {
	color: $common-color;
	font-size: 12px;
	line-height: 20px;
	&:hover {
		color: $common-hover-color;
	}
	i {
		position: relative;
		top: 2px;
	}
}
.sense-no-data {
    color: #909399;
    text-align: center;
    line-height: 69px;
}
.el-tooltip__popper {
	max-width: 800px;
}

/*sense-header-link*/
.sense-header-link {
	margin: 13px 0 15px 0;
}
.sense-header-link span {
	cursor: pointer;
	color: $common-color;
	&:hover {
		color: $common-hover-color;
	}
}

/*sense-header-tip*/
.sense-header-tip {
	border-bottom: 1px solid #e8e8e8;
	padding: 24px;
	margin: 0 -24px;
	position: relative;
	background: url(../img/introduce.png) no-repeat 1030px center;
	background-size: 100px;
}
.sense-header-tip p {
	color: rgba(0, 0, 0, .65);
	line-height: 28px;
}

/*sense-search*/
.sense-search .el-input {
	width: 200px;
}
.sense-search .el-button--medium {
	padding: 10px 15px;
	position: relative;
    top: 2px;
}

/*图标 start*/
.login .ss-lock {
	font-size: 16px;
}
.el-dropdown-menu li .ss-setup {
	font-size: 17px;
}
.sense-table-list .ss-edit {
	font-size: 18px;
}
.sense-table-list .ss-delete {
	font-size: 17px;
}
.sense-table-list .ss-info {
	font-size: 15px;
}
/*图标 end*/
