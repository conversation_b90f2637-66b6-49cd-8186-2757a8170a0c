const state = {
    curPage: 1,
    pageSize: 10,
    fliterPage0: 1, // 0-盗版 fliter-过滤
    fliterSize0: 10,
    fliterPage1: 1, // 1-所有 fliter-过滤
    fliterSize1: 10,
    viewdata0isfliter: false,
    viewdata1isfliter: false,
    // 过滤条件 1-所有
    ipinput1 : "",
    domaininput1 : "",
    lockinput1 : "",
    guidinput1 : "",
    piracyvalue1 : [],
    lockvalue1 :'',
    companyvalue1 :[],
    // 过滤条件 0-盗版
    ipinput0 : "",
    domaininput0 : "",
    lockinput0 : "",
    guidinput0 : "",
    piracyvalue0 : [],
    lockvalue0 :'',
    companyvalue0 :[],
}

const mutations = {
    SET_CUR_PAGE: (state, msg) => {
        state.curPage = msg
    },
    SET_PAGE_SIZE: (state, msg) => {
        state.pageSize = msg
    },
    SET_VIEWDATA0ISFLITER: (state, msg) => {
        state.viewdata0isfliter = msg 
    },
    SET_VIEWDATA1ISFLITER: (state, msg) => {
        state.viewdata1isfliter = msg 
    },
    SET_FLITER_PAGE0: (state, msg) => {
        state.fliterPage0 = msg
    },
    SET_FLITER_SIZE0: (state, msg) => {
        state.fliterSize0 = msg
    },
    SET_FLITER_PAGE1: (state, msg) => {
        state.fliterPage1 = msg
    },
    SET_FLITER_SIZE1: (state, msg) => {
        state.fliterSize1 = msg 
    }
    // 过滤条件 1-所有
    ,
    SET_IPINPUT1: (state, msg) => {
        state.ipinput1 = msg
    },
    SET_DOMAININPUT1: (state, msg) => {
        state.domaininput1 = msg
    },
    SET_LOCKINPUT1: (state, msg) => {
        state.lockinput1 = msg
    },
    SET_GUIDINPUT1: (state, msg) => {
        state.guidinput1 = msg
    },
    SET_PIRACYVALUE1: (state, msg) => {
        state.piracyvalue1 = msg
    },
    SET_LOCKVALUE1: (state, msg) => {
        state.lockvalue1 = msg
    },
    SET_COMPANYVALUE1: (state, msg) => {
        state.companyvalue1 = msg
    },
    // 过滤条件 0-盗版
    SET_IPINPUT0: (state, msg) => {
        state.ipinput0 = msg
    },
    SET_DOMAININPUT0: (state, msg) => {
        state.domaininput0 = msg
    },
    SET_LOCKINPUT0: (state, msg) => {
        state.lockinput0 = msg
    },
    SET_GUIDINPUT0: (state, msg) => {
        state.guidinput0 = msg 
    },
    SET_PIRACYVALUE0: (state, msg) => {
        state.piracyvalue0 = msg 
    },
    SET_LOCKVALUE0: (state, msg) => {
        state.lockvalue0 = msg 
    },
    SET_COMPANYVALUE0: (state, msg) => {
        state.companyvalue0 = msg 
    },
}

const getters = {}

const actions = {}

export default {
    state,
    getters,
    actions,
    mutations
}