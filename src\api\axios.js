import axios from 'axios';

// 创建一个配置对象
const config = {
    baseURL: 'http://127.0.0.1:9249/apm/v1',
    timeout: 10000,
};

// 创建 Axios 实例
const apiClient = axios.create(config);

// 同步获取配置文件
let configLoaded = false;
function loadConfig() {
    if (configLoaded) return Promise.resolve();
    
    return new Promise((resolve) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', '/config.json', false); // 同步请求
        try {
            xhr.send();
            if (xhr.status === 200) {
                const serverConfig = JSON.parse(xhr.responseText);
                config.baseURL = `http://${serverConfig.serverIp}:${serverConfig.serverPort}/apm/v1`;
                apiClient.defaults.baseURL = config.baseURL;
                console.log('配置已加载，服务器地址:', config.baseURL);
            } else {
                console.error('无法加载配置文件，使用默认配置');
            }
        } catch (error) {
            console.error('读取配置文件出错:', error);
        }
        configLoaded = true;
        resolve();
    });
}

// 立即加载配置
loadConfig();

// 请求拦截器确保每次请求前都已加载配置
apiClient.interceptors.request.use(
    async (config) => {
        // 确保配置已加载
        if (!configLoaded) {
            await loadConfig();
        }
        
        // 添加认证令牌
        const token = localStorage.getItem('jwt');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 导出配置和客户端实例
export const serverConfig = config;
export default apiClient;
